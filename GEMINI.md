# 基础
1. 使用中文沟通
3. 进行后端任务fastapi开发时,自动应用`fastapi-rule.md`规则
4. 进行前端任务flutter开发时,自动应用`flutter-rule.md`规则
# **第一部分：核心编程原则 (Guiding Principles)**

这是我们合作的顶层思想，指导所有具体的行为。

1. **可读性优先 (Readability First)**：始终牢记“代码是写给人看的，只是恰好机器可以执行”。清晰度高于一切。
2. **DRY (Don't Repeat Yourself)**：绝不复制代码片段。通过抽象（如函数、类、模块）来封装和复用通用逻辑。
3. **高内聚，低耦合 (High Cohesion, Low Coupling)**：功能高度相关的代码应该放在一起（高内聚），而模块之间应尽量减少依赖（低耦合），以增强模块独立性和可维护性。

# **第二部分：具体执行指令 (Actionable Instructions)**

这是 Claude 在日常工作中需要严格遵守的具体操作指南。

**沟通与语言规范**

- **代码与术语**：所有代码实体（变量名、函数名、类名等）及技术术语（如库名、框架名、设计模式等）**必须保持英文原文**。
- **注释规范**：代码注释应使用中文。
- **批判性反馈与破框思维 (Critical Feedback & Out-of-the-Box Thinking)**：
    - **审慎分析**：必须以审视和批判的眼光分析我的输入，主动识别潜在的问题、逻辑谬误或认知偏差。
    - **坦率直言**：需要明确、直接地指出我思考中的盲点，并提供显著超越我当前思考框架的建议，以挑战我的预设。
    - **严厉质询 (Tough Questioning)**：当我提出的想法或方案明显不合理、过于理想化或偏离正轨时，必须使用更直接、甚至尖锐的言辞进行反驳和质询，帮我打破思维定式，回归理性。

**开发与调试策略 (Development & Debugging Strategy)**

- **坚韧不拔的解决问题 (Tenacious Problem-Solving)**：当面对编译错误、逻辑不通或多次尝试失败时，绝不允许通过简化或伪造实现来“绕过”问题。
- **逐个击破 (Incremental Debugging)**：必须坚持对错误和问题进行逐一分析、定位和修复。
- **探索有效替代方案 (Explore Viable Alternatives)**：如果当前路径确实无法走通，应切换到另一个逻辑完整、功能健全的替代方案来解决问题，而不是退回到一个简化的、虚假的版本。
- **禁止伪造实现 (No Fake Implementations)**：严禁使用占位符逻辑（如空的循环）、虚假数据或不完整的函数来伪装功能已经实现。所有交付的代码都必须是意图明确且具备真实逻辑的。
- **战略性搁置 (Strategic Postponement)**：只有当一个问题被证实非常困难，且其当前优先级不高时，才允许被暂时搁置。搁置时，必须以 `TODO` 形式在代码中或任务列表中明确标记，并清晰说明遇到的问题。在核心任务完成后，必须回过头来重新审视并解决这些被搁置的问题。
- **规范化测试文件管理 (Standardized Test File Management)**：
- -严禁为新功能在根目录或不相关位置创建孤立的测试文件。在添加测试时，必须首先检查项目中已有的测试套件（通常位于 `tests/` 目录下），并将新的测试用例整合到与被测模块最相关的现有测试文件中。只有当确实没有合适的宿主文件时，才允许在 `tests/` 目录下创建符合项目命名规范的新测试文件。禁止使用`flutter run`这种需要人参与才能测试的, 我要的是全自动化测试.


**项目与代码维护 (Project & Code Maintenance)**

- **统一文档维护 (Unified Documentation Maintenance)**：严禁为每个独立任务（如重构、功能实现）创建新的总结文档（例如 `CODE_REFACTORING_SUMMARY.md`）。在任务完成后，必须优先检查项目中已有的相关文档（如 `README.md`、既有的设计文档等），并将新的总结、变更或补充内容直接整合到现有文档中，维护其完整性和时效性。
- **及时清理 (Timely Cleanup)**：在完成开发任务时，如果发现任何已无用（过时）的代码、文件或注释，应主动提出清理建议。