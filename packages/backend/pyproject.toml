[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "cheestack-backend"
version = "2.0.0"
description = "CheeStack 智能学习平台后端服务"
authors = [
    {name = "CheeStack Team", email = "<EMAIL>"}
]
readme = "README.md"
license = {text = "MIT"}
requires-python = ">=3.11"
keywords = ["fastapi", "learning", "education", "api"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Framework :: FastAPI",
    "Topic :: Education",
    "Topic :: Internet :: WWW/HTTP :: HTTP Servers",
]

dependencies = [
    "fastapi==0.111.1",
    "uvicorn[standard]==0.30.3",
    "tortoise-orm==0.21.5",
    "asyncpg==0.29.0",
    "pydantic==2.8.2",
    "pydantic-settings==2.4.0",
    "PyJWT==2.8.0",
    "passlib[bcrypt]==1.7.4",
    "python-dotenv==1.0.1",
    "redis==5.0.8",
    "httpx==0.27.0",
    "shortuuid==1.0.13",
    "arrow==1.3.0",
    "python-multipart==0.0.9",
    "aiofiles==24.1.0",
]

[project.optional-dependencies]
dev = [
    "pytest==8.3.2",
    "pytest-asyncio==0.23.8",
    "pytest-cov==5.0.0",
    "black==24.8.0",
    "isort==5.13.2",
    "flake8==7.1.1",
    "mypy==1.11.1",
    "pre-commit==3.8.0",
]

test = [
    "pytest==8.3.2",
    "pytest-asyncio==0.23.8",
    "pytest-cov==5.0.0",
    "httpx==0.27.0",
    "factory-boy==3.3.0",
]

cloud = [
    "cos-python-sdk-v5==1.9.27",
    "alibabacloud_dysmsapi20170525==2.0.24",
    "qcloud-python-sts==3.1.6",
]

ai = [
    "azure-cognitiveservices-speech==1.41.1",
    "edge-tts==6.1.15",
    "ollama==0.3.3",
]

[project.urls]
Homepage = "https://github.com/cheestack/cheestack"
Documentation = "https://docs.cheestack.com"
Repository = "https://github.com/cheestack/cheestack.git"
Issues = "https://github.com/cheestack/cheestack/issues"

[project.scripts]
cheestack-server = "app.main:main"

[tool.setuptools.packages.find]
where = ["."]
include = ["app*"]

[tool.aerich]
tortoise_orm = "app.core.database.TORTOISE_ORM"
location = "./migrations"
src_folder = "./."

[tool.black]
line-length = 100
target-version = ['py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
  | migrations
)/
'''

[tool.isort]
profile = "black"
line_length = 100
multi_line_output = 3
include_trailing_comma = true
force_grid_wrap = 0
use_parentheses = true
ensure_newline_before_comments = true
skip_glob = ["migrations/*"]

[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true
exclude = [
    "migrations/",
    "tests/",
]

[[tool.mypy.overrides]]
module = [
    "tortoise.*",
    "aerich.*",
    "shortuuid.*",
    "passlib.*",
    "redis.*",
]
ignore_missing_imports = true

[tool.pytest.ini_options]
minversion = "6.0"
addopts = "-ra -q --strict-markers --strict-config"
testpaths = [
    "tests",
]
python_files = [
    "test_*.py",
    "*_test.py",
]
python_classes = [
    "Test*",
]
python_functions = [
    "test_*",
]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
]
asyncio_mode = "auto"

[tool.coverage.run]
source = ["app"]
omit = [
    "*/tests/*",
    "*/migrations/*",
    "*/__pycache__/*",
    "*/venv/*",
    "*/.venv/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]
