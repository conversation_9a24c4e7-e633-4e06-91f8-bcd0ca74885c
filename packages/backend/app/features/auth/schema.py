"""
认证模块数据验证模式
基于ARCHITECTURE.md规范实现的Pydantic数据验证模型
"""
from pydantic import BaseModel, Field, validator
from typing import Optional, List
from datetime import datetime
import re


class UserLoginRequest(BaseModel):
    """用户登录请求"""
    username: str = Field(..., description="用户名/手机号/邮箱")
    password: str = Field(..., min_length=6, description="密码")
    device_id: Optional[str] = Field(None, description="设备ID")
    device_name: Optional[str] = Field(None, description="设备名称")
    device_type: Optional[str] = Field("unknown", description="设备类型")
    platform: Optional[str] = Field("unknown", description="平台")
    app_version: Optional[str] = Field("1.0.0", description="应用版本")


class UserRegisterRequest(BaseModel):
    """用户注册请求"""
    username: Optional[str] = Field(None, min_length=3, max_length=30, description="用户名")
    mobile: str = Field(..., description="手机号")
    password: str = Field(..., min_length=6, description="密码")
    verification_code: str = Field(..., description="验证码")
    nickname: Optional[str] = Field(None, max_length=50, description="昵称")
    
    @validator('mobile')
    def validate_mobile(cls, v):
        if not re.match(r'^1[3-9]\d{9}$', v):
            raise ValueError('手机号格式不正确')
        return v
    
    @validator('username')
    def validate_username(cls, v):
        if v and not re.match(r'^[a-zA-Z0-9_]{3,30}$', v):
            raise ValueError('用户名只能包含字母、数字和下划线，长度3-30位')
        return v


class SMSCodeRequest(BaseModel):
    """短信验证码请求"""
    mobile: str = Field(..., description="手机号")
    code_type: str = Field(..., description="验证码类型: login, register, reset")
    
    @validator('mobile')
    def validate_mobile(cls, v):
        if not re.match(r'^1[3-9]\d{9}$', v):
            raise ValueError('手机号格式不正确')
        return v
    
    @validator('code_type')
    def validate_code_type(cls, v):
        if v not in ['login', 'register', 'reset']:
            raise ValueError('验证码类型不正确')
        return v


class PasswordResetRequest(BaseModel):
    """密码重置请求"""
    mobile: str = Field(..., description="手机号")
    verification_code: str = Field(..., description="验证码")
    new_password: str = Field(..., min_length=6, description="新密码")
    
    @validator('mobile')
    def validate_mobile(cls, v):
        if not re.match(r'^1[3-9]\d{9}$', v):
            raise ValueError('手机号格式不正确')
        return v


class PasswordChangeRequest(BaseModel):
    """密码修改请求"""
    old_password: str = Field(..., description="原密码")
    new_password: str = Field(..., min_length=6, description="新密码")


class UserProfileUpdateRequest(BaseModel):
    """用户资料更新请求"""
    nickname: Optional[str] = Field(None, max_length=50, description="昵称")
    avatar: Optional[str] = Field(None, description="头像URL")
    intro: Optional[str] = Field(None, max_length=500, description="个人简介")
    email: Optional[str] = Field(None, description="邮箱")
    
    @validator('email')
    def validate_email(cls, v):
        if v and not re.match(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$', v):
            raise ValueError('邮箱格式不正确')
        return v


class UserConfigUpdateRequest(BaseModel):
    """用户配置更新请求"""
    is_auto_play_audio: Optional[bool] = Field(None, description="是否自动播放音频")
    is_auto_play_ai_audio: Optional[bool] = Field(None, description="是否自动播放AI音频")
    review_number: Optional[int] = Field(None, ge=1, le=100, description="复习数量")
    study_number: Optional[int] = Field(None, ge=1, le=50, description="学习数量")


# 响应模型
class UserResponse(BaseModel):
    """用户响应"""
    id: str
    username: Optional[str]
    mobile: Optional[str]
    email: Optional[str]
    nickname: Optional[str]
    avatar: Optional[str]
    intro: Optional[str]
    is_active: bool
    last_login: Optional[datetime]
    login_count: int
    created_at: datetime
    updated_at: datetime


class LoginResponse(BaseModel):
    """登录响应"""
    access_token: str
    refresh_token: str
    token_type: str = "bearer"
    expires_in: int
    user: UserResponse


class DeviceResponse(BaseModel):
    """设备响应"""
    id: str
    device_id: str
    device_name: str
    device_type: str
    platform: str
    app_version: str
    last_active: datetime
    is_active: bool
    login_count: int


class UserConfigResponse(BaseModel):
    """用户配置响应"""
    is_auto_play_audio: bool
    is_auto_play_ai_audio: bool
    review_number: int
    study_number: int
    editing_book_id: Optional[str]
    current_study_book_id: Optional[str]


class RoleResponse(BaseModel):
    """角色响应"""
    id: int
    name: str
    code: str
    description: Optional[str]
    is_active: bool


class PermissionResponse(BaseModel):
    """权限响应"""
    id: int
    name: str
    code: str
    description: Optional[str]
    resource: str
    action: str
    is_active: bool


class LoginLogResponse(BaseModel):
    """登录日志响应"""
    id: int
    login_type: str
    ip_address: str
    location: Optional[str]
    is_success: bool
    failure_reason: Optional[str]
    created_at: datetime
    device: Optional[DeviceResponse]


class TokenRefreshRequest(BaseModel):
    """令牌刷新请求"""
    refresh_token: str = Field(..., description="刷新令牌")


class TokenRefreshResponse(BaseModel):
    """令牌刷新响应"""
    access_token: str
    token_type: str = "bearer"
    expires_in: int


# 管理员相关模型
class RoleCreateRequest(BaseModel):
    """角色创建请求"""
    name: str = Field(..., max_length=50, description="角色名称")
    code: str = Field(..., max_length=50, description="角色代码")
    description: Optional[str] = Field(None, max_length=255, description="角色描述")
    permission_ids: List[int] = Field(default=[], description="权限ID列表")


class RoleUpdateRequest(BaseModel):
    """角色更新请求"""
    name: Optional[str] = Field(None, max_length=50, description="角色名称")
    description: Optional[str] = Field(None, max_length=255, description="角色描述")
    is_active: Optional[bool] = Field(None, description="是否启用")
    permission_ids: Optional[List[int]] = Field(None, description="权限ID列表")


class UserRoleUpdateRequest(BaseModel):
    """用户角色更新请求"""
    role_ids: List[int] = Field(..., description="角色ID列表")


class UserStatusUpdateRequest(BaseModel):
    """用户状态更新请求"""
    is_active: bool = Field(..., description="是否激活")
    status: Optional[int] = Field(None, description="用户状态")


# 查询参数模型
class UserListQuery(BaseModel):
    """用户列表查询参数"""
    keyword: Optional[str] = Field(None, description="关键词搜索")
    is_active: Optional[bool] = Field(None, description="是否激活")
    status: Optional[int] = Field(None, description="用户状态")
    role_id: Optional[int] = Field(None, description="角色ID")
    page: int = Field(1, ge=1, description="页码")
    size: int = Field(20, ge=1, le=100, description="每页数量")


class LoginLogQuery(BaseModel):
    """登录日志查询参数"""
    user_id: Optional[str] = Field(None, description="用户ID")
    login_type: Optional[str] = Field(None, description="登录类型")
    is_success: Optional[bool] = Field(None, description="是否成功")
    start_date: Optional[datetime] = Field(None, description="开始日期")
    end_date: Optional[datetime] = Field(None, description="结束日期")
    page: int = Field(1, ge=1, description="页码")
    size: int = Field(20, ge=1, le=100, description="每页数量")
