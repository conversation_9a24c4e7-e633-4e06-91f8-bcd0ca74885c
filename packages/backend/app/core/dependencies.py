"""
统一依赖注入管理
基于ARCHITECTURE.md规范实现的依赖注入系统
"""

from functools import lru_cache
from typing import AsyncGenerator, Annotated
from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from tortoise import connections

from app.core.settings import get_settings
from app.core.security import <PERSON><PERSON><PERSON><PERSON><PERSON>, PasswordManager
from app.core.database import DatabaseManager


# ============= 应用级依赖 (单例) =============


@lru_cache()
def get_settings():
    """获取应用配置 - 单例"""
    from app.core.settings import Settings

    return Settings()


@lru_cache()
def get_jwt_manager() -> JWTManager:
    """获取JWT管理器 - 单例"""
    settings = get_settings()
    return JWTManager(
        secret_key=settings.JWT_SECRET_KEY,
        algorithm=settings.JWT_ALGORITHM,
        access_token_expire_minutes=settings.JWT_ACCESS_TOKEN_EXPIRE_MINUTES,
    )


@lru_cache()
def get_password_manager() -> PasswordManager:
    """获取密码管理器 - 单例"""
    return PasswordManager()


@lru_cache()
def get_database_manager() -> DatabaseManager:
    """获取数据库管理器 - 单例"""
    return DatabaseManager()


# ============= 请求级依赖 =============


async def get_database_connection():
    """获取数据库连接 - 请求级"""
    connection = connections.get("default")
    try:
        yield connection
    finally:
        # 连接会被 Tortoise 自动管理
        pass


# ============= 认证和权限依赖 =============

security = HTTPBearer()


async def get_current_user_id(
    credentials: Annotated[HTTPAuthorizationCredentials, Depends(security)],
    jwt_manager: Annotated[JWTManager, Depends(get_jwt_manager)],
) -> int:
    """获取当前用户ID"""
    try:
        payload = jwt_manager.verify_token(credentials.credentials)
        user_id = payload.get("sub")
        if user_id is None:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="无效的认证凭据",
                headers={"WWW-Authenticate": "Bearer"},
            )
        return int(user_id)
    except Exception:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无效的认证凭据",
            headers={"WWW-Authenticate": "Bearer"},
        )


async def get_current_user(user_id: Annotated[int, Depends(get_current_user_id)]):
    """获取当前用户完整信息"""
    from app.features.auth.models import User

    user = await User.get_or_none(id=user_id)
    if not user:
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="用户不存在")
    return user


async def get_admin_user(current_user=Depends(get_current_user)):
    """获取管理员用户 - 权限检查"""
    if not current_user.is_superuser:
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="需要管理员权限")
    return current_user


# ============= 验证和中间件依赖 =============


def validate_pagination(page: int = 1, size: int = 20, max_size: int = 100):
    """分页参数验证"""
    if page < 1:
        raise HTTPException(status_code=400, detail="页码必须大于0")
    if size < 1 or size > max_size:
        raise HTTPException(status_code=400, detail=f"每页大小必须在1-{max_size}之间")
    return {"page": page, "size": size, "offset": (page - 1) * size}


async def rate_limit_check(request):
    """限流检查 - 可以集成 Redis 等"""
    # 实现限流逻辑
    pass


# ============= 工具类依赖 =============


class DependencyProvider:
    """依赖提供者 - 统一管理复杂依赖"""

    _instances = {}

    @classmethod
    def get_service(cls, service_type: type):
        """获取服务实例"""
        if service_type not in cls._instances:
            cls._instances[service_type] = cls._create_service(service_type)
        return cls._instances[service_type]

    @classmethod
    def _create_service(cls, service_type: type):
        """创建服务实例 - 可以根据类型动态创建"""
        # 这里可以实现更复杂的依赖创建逻辑
        raise ValueError(f"未知的服务类型: {service_type}")
