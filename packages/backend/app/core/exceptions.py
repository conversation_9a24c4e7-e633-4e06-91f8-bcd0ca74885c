"""
异常处理模块
基于ARCHITECTURE.md规范实现的统一异常处理系统
"""
from typing import Any, Union
from fastapi import FastAPI, HTTPException, Request, status
from fastapi.exceptions import RequestValidationError
from fastapi.responses import JSONResponse
from pydantic import ValidationError
from tortoise.exceptions import DoesNotExist, IntegrityError, OperationalError
from tortoise.exceptions import ValidationError as TortoiseValidationError

import logging

logger = logging.getLogger(__name__)


class CheeStackException(Exception):
    """CheeStack自定义异常基类"""
    
    def __init__(self, code: int = -1, msg: str = "操作失败", data: Any = None, status_code: int = 400):
        self.code = code
        self.msg = msg
        self.data = data or []
        self.status_code = status_code
        super().__init__(self.msg)


class AuthenticationError(CheeStackException):
    """认证错误"""
    
    def __init__(self, msg: str = "认证失败"):
        super().__init__(code=401, msg=msg, status_code=401)


class AuthorizationError(CheeStackException):
    """授权错误"""
    
    def __init__(self, msg: str = "权限不足"):
        super().__init__(code=403, msg=msg, status_code=403)


class ValidationError(CheeStackException):
    """数据验证错误"""
    
    def __init__(self, msg: str = "数据验证失败", data: Any = None):
        super().__init__(code=422, msg=msg, data=data, status_code=422)


class NotFoundError(CheeStackException):
    """资源不存在错误"""
    
    def __init__(self, msg: str = "资源不存在"):
        super().__init__(code=404, msg=msg, status_code=404)


class BusinessError(CheeStackException):
    """业务逻辑错误"""
    
    def __init__(self, msg: str = "业务处理失败", code: int = -1):
        super().__init__(code=code, msg=msg, status_code=400)


def add_exception_handle(app: FastAPI):
    """添加异常处理器到FastAPI应用"""
    
    # 自定义异常处理器
    app.add_exception_handler(CheeStackException, cheestack_exception_handler)
    app.add_exception_handler(AuthenticationError, authentication_error_handler)
    app.add_exception_handler(AuthorizationError, authorization_error_handler)
    app.add_exception_handler(ValidationError, validation_error_handler)
    app.add_exception_handler(NotFoundError, not_found_error_handler)
    app.add_exception_handler(BusinessError, business_error_handler)
    
    # FastAPI内置异常处理器
    app.add_exception_handler(HTTPException, http_error_handler)
    app.add_exception_handler(RequestValidationError, request_validation_error_handler)
    
    # Tortoise ORM异常处理器
    app.add_exception_handler(DoesNotExist, tortoise_does_not_exist_handler)
    app.add_exception_handler(IntegrityError, tortoise_integrity_error_handler)
    app.add_exception_handler(TortoiseValidationError, tortoise_validation_error_handler)
    app.add_exception_handler(OperationalError, tortoise_operational_error_handler)
    
    # 全局异常处理器
    app.add_exception_handler(Exception, global_exception_handler)


async def cheestack_exception_handler(request: Request, exc: CheeStackException):
    """CheeStack自定义异常处理器"""
    logger.warning(f"CheeStack异常: {exc.msg}", extra={
        "path": request.url.path,
        "method": request.method,
        "code": exc.code
    })
    
    return JSONResponse(
        content={
            "code": exc.code,
            "msg": exc.msg,
            "data": exc.data,
        },
        status_code=exc.status_code,
    )


async def authentication_error_handler(request: Request, exc: AuthenticationError):
    """认证错误处理器"""
    logger.warning(f"认证失败: {exc.msg}", extra={
        "path": request.url.path,
        "method": request.method
    })
    
    return JSONResponse(
        content={
            "code": exc.code,
            "msg": exc.msg,
            "data": exc.data,
        },
        status_code=exc.status_code,
        headers={"WWW-Authenticate": "Bearer"},
    )


async def authorization_error_handler(request: Request, exc: AuthorizationError):
    """授权错误处理器"""
    logger.warning(f"权限不足: {exc.msg}", extra={
        "path": request.url.path,
        "method": request.method
    })
    
    return JSONResponse(
        content={
            "code": exc.code,
            "msg": exc.msg,
            "data": exc.data,
        },
        status_code=exc.status_code,
    )


async def validation_error_handler(request: Request, exc: ValidationError):
    """数据验证错误处理器"""
    logger.warning(f"数据验证失败: {exc.msg}", extra={
        "path": request.url.path,
        "method": request.method,
        "data": exc.data
    })
    
    return JSONResponse(
        content={
            "code": exc.code,
            "msg": exc.msg,
            "data": exc.data,
        },
        status_code=exc.status_code,
    )


async def not_found_error_handler(request: Request, exc: NotFoundError):
    """资源不存在错误处理器"""
    logger.info(f"资源不存在: {exc.msg}", extra={
        "path": request.url.path,
        "method": request.method
    })
    
    return JSONResponse(
        content={
            "code": exc.code,
            "msg": exc.msg,
            "data": exc.data,
        },
        status_code=exc.status_code,
    )


async def business_error_handler(request: Request, exc: BusinessError):
    """业务逻辑错误处理器"""
    logger.warning(f"业务错误: {exc.msg}", extra={
        "path": request.url.path,
        "method": request.method,
        "code": exc.code
    })
    
    return JSONResponse(
        content={
            "code": exc.code,
            "msg": exc.msg,
            "data": exc.data,
        },
        status_code=exc.status_code,
    )


async def http_error_handler(request: Request, exc: HTTPException):
    """HTTP异常处理器"""
    logger.warning(f"HTTP异常: {exc.detail}", extra={
        "path": request.url.path,
        "method": request.method,
        "status_code": exc.status_code
    })
    
    return JSONResponse(
        content={
            "code": exc.status_code,
            "msg": exc.detail,
            "data": [],
        },
        status_code=exc.status_code,
        headers=exc.headers,
    )


async def request_validation_error_handler(
    request: Request,
    exc: Union[RequestValidationError, ValidationError],
) -> JSONResponse:
    """请求参数验证错误处理器"""
    logger.warning(f"参数验证错误: {str(exc)}", extra={
        "path": request.url.path,
        "method": request.method,
        "errors": exc.errors() if hasattr(exc, 'errors') else str(exc)
    })
    
    return JSONResponse(
        content={
            "code": status.HTTP_422_UNPROCESSABLE_ENTITY,
            "msg": "参数验证失败",
            "data": exc.errors() if hasattr(exc, 'errors') else [str(exc)],
        },
        status_code=422,
    )


async def tortoise_does_not_exist_handler(request: Request, exc: DoesNotExist):
    """Tortoise ORM 对象不存在异常处理器"""
    logger.info(f"数据不存在: {str(exc)}", extra={
        "path": request.url.path,
        "method": request.method
    })
    
    return JSONResponse(
        content={
            "code": 404,
            "msg": "数据不存在",
            "data": [],
        },
        status_code=404,
    )


async def tortoise_integrity_error_handler(request: Request, exc: IntegrityError):
    """Tortoise ORM 完整性错误处理器"""
    logger.error(f"数据完整性错误: {str(exc)}", extra={
        "path": request.url.path,
        "method": request.method
    })
    
    return JSONResponse(
        content={
            "code": 422,
            "msg": "数据完整性错误",
            "data": [],
        },
        status_code=422,
    )


async def tortoise_validation_error_handler(request: Request, exc: TortoiseValidationError):
    """Tortoise ORM 验证错误处理器"""
    logger.warning(f"数据库验证错误: {str(exc)}", extra={
        "path": request.url.path,
        "method": request.method
    })
    
    return JSONResponse(
        content={
            "code": 422,
            "msg": "数据验证错误",
            "data": [],
        },
        status_code=422,
    )


async def tortoise_operational_error_handler(request: Request, exc: OperationalError):
    """Tortoise ORM 操作错误处理器"""
    logger.error(f"数据库操作错误: {str(exc)}", extra={
        "path": request.url.path,
        "method": request.method
    })
    
    return JSONResponse(
        content={
            "code": 500,
            "msg": "数据库操作失败",
            "data": [],
        },
        status_code=500,
    )


async def global_exception_handler(request: Request, exc: Exception):
    """全局异常处理器"""
    logger.error(f"未处理的异常: {str(exc)}", extra={
        "path": request.url.path,
        "method": request.method,
        "exception_type": type(exc).__name__
    }, exc_info=True)
    
    return JSONResponse(
        content={
            "code": 500,
            "msg": "服务器内部错误",
            "data": [],
        },
        status_code=500,
    )
