"""
应用事件处理模块
基于ARCHITECTURE.md规范实现的事件监听系统
"""
import logging
from typing import Callable
from fastapi import FastAPI

from app.core.database import register_database, init_database, close_database
from app.core.settings import get_settings

logger = logging.getLogger(__name__)
settings = get_settings()


def startup(app: FastAPI) -> Callable:
    """
    FastAPI 启动完成事件
    :param app: FastAPI应用实例
    :return: 启动事件处理函数
    """
    
    async def app_start() -> None:
        """应用启动处理"""
        try:
            logger.info("开始启动CheeStack应用...")
            
            # 初始化数据库连接
            logger.info("初始化数据库连接...")
            register_database(app)
            
            # 初始化Redis连接
            logger.info("初始化Redis连接...")
            await init_redis()
            
            # 初始化其他服务
            logger.info("初始化其他服务...")
            await init_external_services()
            
            # 创建必要的目录
            await create_directories()
            
            # 执行数据库迁移检查
            if settings.DEBUG:
                logger.info("开发环境：检查数据库迁移...")
                await check_database_migrations()
            
            logger.info(f"CheeStack应用启动完成 - 版本: {settings.APP_VERSION}")
            
        except Exception as e:
            logger.error(f"应用启动失败: {str(e)}", exc_info=True)
            raise
    
    return app_start


def stopping(app: FastAPI) -> Callable:
    """
    FastAPI 停止事件
    :param app: FastAPI应用实例
    :return: 停止事件处理函数
    """
    
    async def stop_app() -> None:
        """应用停止处理"""
        try:
            logger.info("开始停止CheeStack应用...")
            
            # 关闭数据库连接
            logger.info("关闭数据库连接...")
            await close_database()
            
            # 关闭Redis连接
            logger.info("关闭Redis连接...")
            await close_redis()
            
            # 清理其他资源
            logger.info("清理其他资源...")
            await cleanup_resources()
            
            logger.info("CheeStack应用已安全停止")
            
        except Exception as e:
            logger.error(f"应用停止时发生错误: {str(e)}", exc_info=True)
    
    return stop_app


async def init_redis():
    """初始化Redis连接"""
    try:
        import redis.asyncio as redis
        
        # 创建Redis连接池
        redis_pool = redis.ConnectionPool.from_url(
            settings.REDIS_URL,
            encoding="utf-8",
            decode_responses=True,
            max_connections=20
        )
        
        # 测试连接
        redis_client = redis.Redis(connection_pool=redis_pool)
        await redis_client.ping()
        
        # 将Redis客户端存储到应用状态中
        # 这里可以使用依赖注入或全局变量
        logger.info("Redis连接初始化成功")
        
    except Exception as e:
        logger.error(f"Redis连接初始化失败: {str(e)}")
        # 根据需要决定是否抛出异常
        # raise


async def close_redis():
    """关闭Redis连接"""
    try:
        # 这里实现Redis连接的清理逻辑
        logger.info("Redis连接已关闭")
    except Exception as e:
        logger.error(f"关闭Redis连接时发生错误: {str(e)}")


async def init_external_services():
    """初始化外部服务"""
    try:
        # 初始化腾讯云服务
        if settings.TENCENT_SECRET_ID and settings.TENCENT_SECRET_KEY:
            logger.info("初始化腾讯云服务...")
            # 这里可以初始化腾讯云SDK
        
        # 初始化阿里云服务
        if settings.ALIYUN_ACCESS_KEY_ID and settings.ALIYUN_ACCESS_KEY_SECRET:
            logger.info("初始化阿里云服务...")
            # 这里可以初始化阿里云SDK
        
        logger.info("外部服务初始化完成")
        
    except Exception as e:
        logger.error(f"外部服务初始化失败: {str(e)}")


async def create_directories():
    """创建必要的目录"""
    import os
    
    try:
        directories = [
            settings.STATIC_DIR,
            settings.UPLOAD_DIR,
            os.path.join(settings.UPLOAD_DIR, "images"),
            os.path.join(settings.UPLOAD_DIR, "audio"),
            os.path.join(settings.UPLOAD_DIR, "video"),
            os.path.join(settings.UPLOAD_DIR, "documents"),
        ]
        
        for directory in directories:
            os.makedirs(directory, exist_ok=True)
        
        logger.info("必要目录创建完成")
        
    except Exception as e:
        logger.error(f"创建目录失败: {str(e)}")


async def check_database_migrations():
    """检查数据库迁移状态"""
    try:
        # 这里可以检查数据库迁移状态
        # 例如检查是否有未执行的迁移
        logger.info("数据库迁移检查完成")
        
    except Exception as e:
        logger.warning(f"数据库迁移检查失败: {str(e)}")


async def cleanup_resources():
    """清理资源"""
    try:
        # 清理临时文件
        # 关闭文件句柄
        # 清理缓存
        logger.info("资源清理完成")
        
    except Exception as e:
        logger.error(f"资源清理失败: {str(e)}")


# 健康检查相关事件
async def health_check_startup():
    """健康检查启动初始化"""
    logger.info("健康检查服务已启动")


async def health_check_shutdown():
    """健康检查停止清理"""
    logger.info("健康检查服务已停止")
