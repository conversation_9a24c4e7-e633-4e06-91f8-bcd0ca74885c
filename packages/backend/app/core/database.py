"""
数据库配置和连接管理
基于ARCHITECTURE.md规范实现的数据库管理系统
"""
from fastapi import FastAPI
from tortoise import Tortoise
from tortoise.contrib.fastapi import register_tortoise
from tortoise.transactions import in_transaction
from typing import Optional, Dict, Any
from tortoise.models import Model

from app.core.settings import get_settings

settings = get_settings()

# Tortoise ORM 配置
TORTOISE_ORM = {
    "connections": {
        "default": {
            "engine": "tortoise.backends.asyncpg",
            "credentials": {
                "host": settings.DATABASE_HOST,
                "port": settings.DATABASE_PORT,
                "user": settings.DATABASE_USER,
                "password": settings.DATABASE_PASSWORD,
                "database": settings.DATABASE_NAME,
                "minsize": 1,
                "maxsize": 20,
            }
        }
    },
    "apps": {
        "models": {
            "models": [
                "app.features.auth.models",
                "app.features.study.models",
                "app.features.general.models",
                "app.features.pronunciation.models",
                "aerich.models"
            ],
            "default_connection": "default",
        }
    },
    "use_tz": True,
    "timezone": "UTC",
}


async def init_database():
    """初始化数据库连接"""
    await Tortoise.init(config=TORTOISE_ORM)
    

async def generate_schemas():
    """生成数据库表结构"""
    await Tortoise.generate_schemas()


async def close_database():
    """关闭数据库连接"""
    await Tortoise.close_connections()


def register_database(app: FastAPI):
    """注册数据库到 FastAPI 应用"""
    register_tortoise(
        app,
        config=TORTOISE_ORM,
        generate_schemas=settings.DEBUG,  # 仅在开发环境自动生成表
        add_exception_handlers=True,
    )


# 数据库工具函数
async def get_or_create(model_class: Model, defaults: Optional[Dict[str, Any]] = None, **kwargs):
    """获取或创建模型实例"""
    try:
        instance = await model_class.get(**kwargs)
        return instance, False
    except model_class.DoesNotExist:
        create_kwargs = {**kwargs, **(defaults or {})}
        instance = await model_class.create(**create_kwargs)
        return instance, True


async def bulk_create_or_update(model_class: Model, data_list: list, update_fields: list = None):
    """批量创建或更新"""
    async with in_transaction() as conn:
        # 批量操作逻辑
        pass


class DatabaseManager:
    """数据库管理器 - 处理连接池、事务等"""
    
    @staticmethod
    async def health_check() -> bool:
        """数据库健康检查"""
        try:
            await Tortoise.get_connection("default").execute_query("SELECT 1")
            return True
        except Exception:
            return False
    
    @staticmethod
    async def get_connection_info():
        """获取连接信息"""
        conn = Tortoise.get_connection("default")
        return {
            "engine": conn.__class__.__name__,
            "database": conn.database,
        }
