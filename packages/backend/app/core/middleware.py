"""
中间件模块
基于ARCHITECTURE.md规范实现的中间件系统
"""
import time
import uuid
import logging
from typing import Callable
from fastapi import FastAPI, Request, Response
from fastapi.middleware.cors import CORSMiddleware
from starlette.datastructures import MutableHeaders
from starlette.types import ASGIApp, Message, Receive, Scope, Send
from starlette.middleware.sessions import SessionMiddleware
from starlette.middleware.base import BaseHTTPMiddleware

from app.core.settings import get_settings

logger = logging.getLogger(__name__)
settings = get_settings()


def add_middleware(app: FastAPI):
    """添加中间件到FastAPI应用"""
    
    # CORS中间件
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.CORS_ORIGINS,
        allow_credentials=settings.CORS_ALLOW_CREDENTIALS,
        allow_methods=settings.CORS_ALLOW_METHODS,
        allow_headers=settings.CORS_ALLOW_HEADERS,
    )
    
    # 请求追踪中间件
    app.add_middleware(RequestTrackingMiddleware)
    
    # 性能监控中间件
    app.add_middleware(PerformanceMiddleware)
    
    # 日志中间件
    app.add_middleware(LoggingMiddleware)
    
    # Session中间件
    app.add_middleware(
        SessionMiddleware,
        secret_key=settings.SECRET_KEY,
        session_cookie="session_id",
        max_age=14 * 24 * 60 * 60,  # 14天
    )
    
    # 安全头中间件
    app.add_middleware(SecurityHeadersMiddleware)


class RequestTrackingMiddleware(BaseHTTPMiddleware):
    """请求追踪中间件"""
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # 生成请求ID
        request_id = str(uuid.uuid4())
        request.state.request_id = request_id
        
        # 添加请求ID到响应头
        response = await call_next(request)
        response.headers["X-Request-ID"] = request_id
        
        return response


class PerformanceMiddleware(BaseHTTPMiddleware):
    """性能监控中间件"""
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        start_time = time.time()
        
        response = await call_next(request)
        
        process_time = time.time() - start_time
        response.headers["X-Process-Time"] = str(process_time)
        
        # 记录慢请求
        if process_time > 1.0:  # 超过1秒的请求
            logger.warning(f"慢请求: {request.method} {request.url.path} - {process_time:.2f}s", extra={
                "request_id": getattr(request.state, "request_id", "unknown"),
                "method": request.method,
                "path": request.url.path,
                "process_time": process_time
            })
        
        return response


class LoggingMiddleware(BaseHTTPMiddleware):
    """日志中间件"""
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # 记录请求开始
        logger.info(f"请求开始: {request.method} {request.url.path}", extra={
            "request_id": getattr(request.state, "request_id", "unknown"),
            "method": request.method,
            "path": request.url.path,
            "query_params": str(request.query_params),
            "client_ip": request.client.host if request.client else "unknown",
            "user_agent": request.headers.get("user-agent", "unknown")
        })
        
        try:
            response = await call_next(request)
            
            # 记录请求完成
            logger.info(f"请求完成: {request.method} {request.url.path} - {response.status_code}", extra={
                "request_id": getattr(request.state, "request_id", "unknown"),
                "method": request.method,
                "path": request.url.path,
                "status_code": response.status_code
            })
            
            return response
            
        except Exception as e:
            # 记录请求异常
            logger.error(f"请求异常: {request.method} {request.url.path} - {str(e)}", extra={
                "request_id": getattr(request.state, "request_id", "unknown"),
                "method": request.method,
                "path": request.url.path,
                "error": str(e)
            }, exc_info=True)
            
            raise


class SecurityHeadersMiddleware(BaseHTTPMiddleware):
    """安全头中间件"""
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        response = await call_next(request)
        
        # 添加安全头
        response.headers["X-Content-Type-Options"] = "nosniff"
        response.headers["X-Frame-Options"] = "DENY"
        response.headers["X-XSS-Protection"] = "1; mode=block"
        response.headers["Referrer-Policy"] = "strict-origin-when-cross-origin"
        
        # 在生产环境中添加HSTS头
        if not settings.DEBUG:
            response.headers["Strict-Transport-Security"] = "max-age=31536000; includeSubDomains"
        
        return response


class RateLimitMiddleware(BaseHTTPMiddleware):
    """限流中间件"""
    
    def __init__(self, app: ASGIApp, calls: int = 100, period: int = 60):
        super().__init__(app)
        self.calls = calls
        self.period = period
        self.clients = {}
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        if not settings.RATE_LIMIT_ENABLED:
            return await call_next(request)
        
        client_ip = request.client.host if request.client else "unknown"
        current_time = time.time()
        
        # 清理过期的记录
        self.clients = {
            ip: timestamps for ip, timestamps in self.clients.items()
            if any(t > current_time - self.period for t in timestamps)
        }
        
        # 检查当前客户端的请求频率
        if client_ip in self.clients:
            # 过滤出时间窗口内的请求
            recent_requests = [
                t for t in self.clients[client_ip]
                if t > current_time - self.period
            ]
            
            if len(recent_requests) >= self.calls:
                logger.warning(f"限流触发: {client_ip} - {len(recent_requests)} requests", extra={
                    "client_ip": client_ip,
                    "request_count": len(recent_requests),
                    "path": request.url.path
                })
                
                from fastapi import HTTPException
                raise HTTPException(status_code=429, detail="请求过于频繁，请稍后再试")
            
            self.clients[client_ip] = recent_requests + [current_time]
        else:
            self.clients[client_ip] = [current_time]
        
        return await call_next(request)


class CustomBaseMiddleware:
    """自定义基础中间件（兼容原有逻辑）"""
    
    def __init__(self, app: ASGIApp) -> None:
        self.app = app
    
    async def __call__(self, scope: Scope, receive: Receive, send: Send) -> None:
        # 如果请求类型不是http，直接返回
        if scope["type"] != "http":
            await self.app(scope, receive, send)
            return
        
        # 记录开始处理的时间
        start_time = time.time()
        
        # 创建Request请求实例
        req = Request(scope, receive, send)
        
        # 如果没有获取到session，则添加一个session
        if not req.session.get("session"):
            req.session.setdefault("session", str(uuid.uuid4()))
        
        async def send_wrapper(message: Message) -> None:
            process_time = time.time() - start_time
            if message["type"] == "http.response.start":
                headers = MutableHeaders(scope=message)
                headers.append("X-Process-Time", str(process_time))
            await send(message)
        
        await self.app(scope, receive, send_wrapper)
