"""
CheeStack FastAPI 应用入口
基于ARCHITECTURE.md规范重构的主应用文件
"""
import pytz
from fastapi import FastAPI
from app.core import exceptions, middleware, events
from app.core.routers import AppRoutes


def create_app() -> FastAPI:
    """创建FastAPI应用实例"""
    app = FastAPI(
        title="CheeStack API",
        description="智能学习平台后端服务",
        version="2.0.0",
        docs_url="/docs",
        redoc_url="/redoc"
    )
    
    # 设置当前时区为东八区（北京时间）
    timezone = pytz.timezone("Asia/Shanghai")
    
    # 添加路由
    app.include_router(AppRoutes)
    
    # 添加中间件
    middleware.add_middleware(app)
    
    # 添加异常处理
    exceptions.add_exception_handle(app)
    
    # 事件监听
    app.add_event_handler("startup", events.startup(app))
    app.add_event_handler("shutdown", events.stopping(app))
    
    return app


# 创建应用实例
app = create_app()

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True
    )
