/// Flutter应用测试
/// 基于ARCHITECTURE.md规范实现的Widget测试
library;

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';

import 'package:cheestack_frontend/main.dart';
import 'package:cheestack_frontend/core/services/app_service.dart';

void main() {
  group('CheeStack App Tests', () {
    setUp(() {
      // 重置GetX状态
      Get.reset();
    });

    testWidgets('App should start without crashing', (WidgetTester tester) async {
      // 模拟AppService初始化
      Get.put<AppService>(MockAppService());
      
      // 构建应用
      await tester.pumpWidget(const CheeStackApp());
      
      // 等待动画完成
      await tester.pumpAndSettle();
      
      // 验证应用启动成功
      expect(find.byType(MaterialApp), findsOneWidget);
    });

    testWidgets('Splash page should display app name', (WidgetTester tester) async {
      // 模拟AppService初始化
      Get.put<AppService>(MockAppService());
      
      // 构建应用
      await tester.pumpWidget(const CheeStackApp());
      
      // 等待初始渲染
      await tester.pump();
      
      // 验证启动页显示应用名称
      expect(find.text('CheeStack'), findsOneWidget);
    });

    testWidgets('Error app should display error message', (WidgetTester tester) async {
      const errorMessage = 'Test error message';
      
      // 构建错误应用
      await tester.pumpWidget(const ErrorApp(error: errorMessage));
      
      // 验证错误信息显示
      expect(find.text('应用启动失败'), findsOneWidget);
      expect(find.text(errorMessage), findsOneWidget);
      expect(find.text('重新启动'), findsOneWidget);
    });

    testWidgets('Not found page should display 404 message', (WidgetTester tester) async {
      // 构建404页面
      await tester.pumpWidget(
        MaterialApp(
          home: const NotFoundPage(),
        ),
      );
      
      // 验证404页面内容
      expect(find.text('页面不存在'), findsAtLeastNWidget(1));
      expect(find.text('您访问的页面不存在或已被删除'), findsOneWidget);
      expect(find.text('返回首页'), findsOneWidget);
    });
  });

  group('App Configuration Tests', () {
    test('App config should have correct values', () {
      // 这里可以测试应用配置
      expect(true, isTrue); // 占位测试
    });
  });
}

/// 模拟AppService用于测试
class MockAppService extends GetxService implements AppService {
  @override
  PackageInfo? get packageInfo => null;

  @override
  Map<String, dynamic>? get deviceInfo => null;

  @override
  bool get isFirstLaunch => false;

  @override
  Future<void> onInit() async {
    super.onInit();
    // 模拟初始化完成
  }

  @override
  String getVersionInfo() => '2.0.0+1';

  @override
  String getAppName() => 'CheeStack';

  @override
  String getPackageName() => 'com.cheestack.app';

  @override
  String getBuildNumber() => '1';

  @override
  Future<String> getDeviceId() async => 'test-device-id';

  @override
  Future<void> clearAppData() async {}

  @override
  Future<void> restartApp() async {}

  @override
  Future<bool> checkForUpdates() async => false;

  @override
  Future<Map<String, dynamic>> getAppStats() async => {};
}
