name: cheestack_frontend
description: CheeStack 智能学习平台前端应用
publish_to: "none"
version: 2.0.0+1

environment:
  sdk: ">=3.0.0 <4.0.0"
  flutter: ">=3.10.0"

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  # 核心依赖
  get: ^4.6.6                    # 状态管理和路由
  dio: ^5.4.3+1                  # HTTP客户端
  shared_preferences: ^2.2.3     # 本地存储
  logger: ^2.4.0                 # 日志记录
  
  # UI组件
  cupertino_icons: ^1.0.8
  flutter_screenutil: ^5.9.3     # 屏幕适配
  flutter_easyloading: ^3.0.5    # 加载提示
  extended_image: ^8.2.1         # 图片组件
  pull_to_refresh_flutter3: ^2.0.2 # 下拉刷新
  
  # 数据处理
  json_annotation: ^4.9.0
  json_serializable: ^6.8.0
  freezed_annotation: ^2.4.4
  equatable: ^2.0.5
  
  # 工具类
  package_info_plus: ^8.0.2      # 应用信息
  device_info_plus: ^10.1.2      # 设备信息
  permission_handler: ^11.3.1    # 权限管理
  connectivity_plus: ^6.0.5      # 网络状态
  url_launcher: ^6.3.0           # URL启动
  
  # 文件和媒体
  file_picker: ^8.1.2            # 文件选择
  image_picker: ^1.1.2           # 图片选择
  path_provider: ^2.1.4          # 路径获取
  
  # 音频相关
  just_audio: ^0.9.40
  audio_session: ^0.1.21
  flutter_sound: ^9.25.1
  record: ^5.1.2
  
  # 数据库
  sqflite: ^2.4.2
  
  # 加密和安全
  crypto: ^3.0.5
  uuid: ^4.5.1
  
  # 国际化
  intl: ^0.19.0
  
  # 其他UI组件
  flutter_slidable: ^3.1.1
  smooth_page_indicator: ^1.2.0+3
  auto_size_text: ^3.0.0
  flutter_rating_bar: ^4.0.1
  pinput: ^5.0.1
  
  # 动画
  lottie: ^3.1.2
  
  # 网络和缓存
  dio_cache_interceptor: ^3.5.0
  flutter_cache_manager: ^3.4.1

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^4.0.0
  
  # 代码生成
  build_runner: ^2.4.12
  json_serializable: ^6.8.0
  freezed: ^2.5.7
  
  # 测试工具
  mockito: ^5.4.4
  integration_test:
    sdk: flutter

flutter:
  uses-material-design: true
  
  assets:
    - assets/images/
    - assets/lotties/
    - assets/audios/
    - assets/fonts/
    - assets/i18n/
  
  fonts:
    - family: NotoSans
      fonts:
        - asset: assets/fonts/NotoSans-Regular.ttf
          weight: 400
        - asset: assets/fonts/NotoSans-Bold.ttf
          weight: 700

# 代码生成配置
json_to_dart:
  output_folder: lib/shared/models
  type_checking: true
  null_value_data_type: String
  null_safety: true
  copy_with_method: true
  merge_array_approach: true
  check_number_as_num: true
