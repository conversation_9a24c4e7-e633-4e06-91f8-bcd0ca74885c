/// 设备工具类
/// 基于ARCHITECTURE.md规范实现的设备信息获取和管理
library;

import 'dart:io';
import 'dart:math';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:uuid/uuid.dart';
import 'package:crypto/crypto.dart';
import 'dart:convert';

/// 设备工具类
class DeviceUtils {
  static final DeviceInfoPlugin _deviceInfo = DeviceInfoPlugin();
  static const Uuid _uuid = Uuid();

  /// 获取设备信息
  static Future<Map<String, dynamic>> getDeviceInfo() async {
    final Map<String, dynamic> deviceData = {};

    try {
      if (Platform.isAndroid) {
        final androidInfo = await _deviceInfo.androidInfo;
        deviceData.addAll({
          'platform': 'Android',
          'model': androidInfo.model,
          'manufacturer': androidInfo.manufacturer,
          'brand': androidInfo.brand,
          'device': androidInfo.device,
          'product': androidInfo.product,
          'androidId': androidInfo.id,
          'version': androidInfo.version.release,
          'sdkInt': androidInfo.version.sdkInt,
          'board': androidInfo.board,
          'bootloader': androidInfo.bootloader,
          'display': androidInfo.display,
          'fingerprint': androidInfo.fingerprint,
          'hardware': androidInfo.hardware,
          'host': androidInfo.host,
          'tags': androidInfo.tags,
          'type': androidInfo.type,
          'isPhysicalDevice': androidInfo.isPhysicalDevice,
          'systemFeatures': androidInfo.systemFeatures,
        });
      } else if (Platform.isIOS) {
        final iosInfo = await _deviceInfo.iosInfo;
        deviceData.addAll({
          'platform': 'iOS',
          'name': iosInfo.name,
          'systemName': iosInfo.systemName,
          'systemVersion': iosInfo.systemVersion,
          'model': iosInfo.model,
          'localizedModel': iosInfo.localizedModel,
          'identifierForVendor': iosInfo.identifierForVendor,
          'isPhysicalDevice': iosInfo.isPhysicalDevice,
          'utsname': {
            'sysname': iosInfo.utsname.sysname,
            'nodename': iosInfo.utsname.nodename,
            'release': iosInfo.utsname.release,
            'version': iosInfo.utsname.version,
            'machine': iosInfo.utsname.machine,
          },
        });
      } else if (Platform.isWindows) {
        final windowsInfo = await _deviceInfo.windowsInfo;
        deviceData.addAll({
          'platform': 'Windows',
          'computerName': windowsInfo.computerName,
          'numberOfCores': windowsInfo.numberOfCores,
          'systemMemoryInMegabytes': windowsInfo.systemMemoryInMegabytes,
          'userName': windowsInfo.userName,
          'majorVersion': windowsInfo.majorVersion,
          'minorVersion': windowsInfo.minorVersion,
          'buildNumber': windowsInfo.buildNumber,
          'platformId': windowsInfo.platformId,
          'csdVersion': windowsInfo.csdVersion,
          'servicePackMajor': windowsInfo.servicePackMajor,
          'servicePackMinor': windowsInfo.servicePackMinor,
          'suitMask': windowsInfo.suitMask,
          'productType': windowsInfo.productType,
          'reserved': windowsInfo.reserved,
          'buildLab': windowsInfo.buildLab,
          'buildLabEx': windowsInfo.buildLabEx,
          'digitalProductId': windowsInfo.digitalProductId,
          'displayVersion': windowsInfo.displayVersion,
          'editionId': windowsInfo.editionId,
          'installDate': windowsInfo.installDate.toIso8601String(),
          'productId': windowsInfo.productId,
          'productName': windowsInfo.productName,
          'registeredOwner': windowsInfo.registeredOwner,
          'releaseId': windowsInfo.releaseId,
          'deviceId': windowsInfo.deviceId,
        });
      } else if (Platform.isMacOS) {
        final macOsInfo = await _deviceInfo.macOsInfo;
        deviceData.addAll({
          'platform': 'macOS',
          'computerName': macOsInfo.computerName,
          'hostName': macOsInfo.hostName,
          'arch': macOsInfo.arch,
          'model': macOsInfo.model,
          'kernelVersion': macOsInfo.kernelVersion,
          'majorVersion': macOsInfo.majorVersion,
          'minorVersion': macOsInfo.minorVersion,
          'patchVersion': macOsInfo.patchVersion,
          'osRelease': macOsInfo.osRelease,
          'activeCPUs': macOsInfo.activeCPUs,
          'memorySize': macOsInfo.memorySize,
          'cpuFrequency': macOsInfo.cpuFrequency,
          'systemGUID': macOsInfo.systemGUID,
        });
      } else if (Platform.isLinux) {
        final linuxInfo = await _deviceInfo.linuxInfo;
        deviceData.addAll({
          'platform': 'Linux',
          'name': linuxInfo.name,
          'version': linuxInfo.version,
          'id': linuxInfo.id,
          'idLike': linuxInfo.idLike,
          'versionCodename': linuxInfo.versionCodename,
          'versionId': linuxInfo.versionId,
          'prettyName': linuxInfo.prettyName,
          'buildId': linuxInfo.buildId,
          'variant': linuxInfo.variant,
          'variantId': linuxInfo.variantId,
          'machineId': linuxInfo.machineId,
        });
      }
    } catch (e) {
      deviceData['error'] = e.toString();
    }

    return deviceData;
  }

  /// 生成设备唯一标识符
  static Future<String> generateDeviceId() async {
    try {
      final deviceInfo = await getDeviceInfo();
      final platform = deviceInfo['platform'] ?? 'unknown';

      String identifier = '';

      if (Platform.isAndroid) {
        identifier = deviceInfo['androidId'] ?? '';
      } else if (Platform.isIOS) {
        identifier = deviceInfo['identifierForVendor'] ?? '';
      } else if (Platform.isWindows) {
        identifier = deviceInfo['deviceId'] ?? deviceInfo['computerName'] ?? '';
      } else if (Platform.isMacOS) {
        identifier =
            deviceInfo['systemGUID'] ?? deviceInfo['computerName'] ?? '';
      } else if (Platform.isLinux) {
        identifier = deviceInfo['machineId'] ?? deviceInfo['id'] ?? '';
      }

      // 如果无法获取系统标识符，则生成一个随机UUID
      if (identifier.isEmpty) {
        identifier = _uuid.v4();
      }

      // 使用SHA-256哈希生成最终的设备ID
      final bytes = utf8.encode('$platform-$identifier');
      final digest = sha256.convert(bytes);

      return digest.toString();
    } catch (e) {
      // 如果所有方法都失败，返回一个随机UUID
      return _uuid.v4();
    }
  }

  /// 获取设备类型
  static String getDeviceType() {
    if (Platform.isAndroid) {
      return 'Android';
    } else if (Platform.isIOS) {
      return 'iOS';
    } else if (Platform.isWindows) {
      return 'Windows';
    } else if (Platform.isMacOS) {
      return 'macOS';
    } else if (Platform.isLinux) {
      return 'Linux';
    } else if (Platform.isFuchsia) {
      return 'Fuchsia';
    } else {
      return 'Unknown';
    }
  }

  /// 获取操作系统版本
  static Future<String> getOSVersion() async {
    try {
      final deviceInfo = await getDeviceInfo();

      if (Platform.isAndroid) {
        return deviceInfo['version'] ?? 'Unknown';
      } else if (Platform.isIOS) {
        return deviceInfo['systemVersion'] ?? 'Unknown';
      } else if (Platform.isWindows) {
        final major = deviceInfo['majorVersion'] ?? 0;
        final minor = deviceInfo['minorVersion'] ?? 0;
        final build = deviceInfo['buildNumber'] ?? 0;
        return '$major.$minor.$build';
      } else if (Platform.isMacOS) {
        final major = deviceInfo['majorVersion'] ?? 0;
        final minor = deviceInfo['minorVersion'] ?? 0;
        final patch = deviceInfo['patchVersion'] ?? 0;
        return '$major.$minor.$patch';
      } else if (Platform.isLinux) {
        return deviceInfo['version'] ?? 'Unknown';
      }

      return 'Unknown';
    } catch (e) {
      return 'Unknown';
    }
  }

  /// 获取设备名称
  static Future<String> getDeviceName() async {
    try {
      final deviceInfo = await getDeviceInfo();

      if (Platform.isAndroid) {
        final manufacturer = deviceInfo['manufacturer'] ?? '';
        final model = deviceInfo['model'] ?? '';
        return '$manufacturer $model'.trim();
      } else if (Platform.isIOS) {
        return deviceInfo['name'] ?? 'iOS Device';
      } else if (Platform.isWindows) {
        return deviceInfo['computerName'] ?? 'Windows PC';
      } else if (Platform.isMacOS) {
        return deviceInfo['computerName'] ?? 'Mac';
      } else if (Platform.isLinux) {
        return deviceInfo['prettyName'] ?? 'Linux';
      }

      return 'Unknown Device';
    } catch (e) {
      return 'Unknown Device';
    }
  }

  /// 检查是否为物理设备
  static Future<bool> isPhysicalDevice() async {
    try {
      final deviceInfo = await getDeviceInfo();
      return deviceInfo['isPhysicalDevice'] ?? true;
    } catch (e) {
      return true;
    }
  }

  /// 生成随机字符串
  static String generateRandomString(int length) {
    const chars =
        'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    final random = Random();
    return String.fromCharCodes(
      Iterable.generate(
          length, (_) => chars.codeUnitAt(random.nextInt(chars.length))),
    );
  }

  /// 格式化设备信息为字符串
  static String formatDeviceInfo(Map<String, dynamic> deviceInfo) {
    final buffer = StringBuffer();

    deviceInfo.forEach((key, value) {
      if (value != null) {
        buffer.writeln('$key: $value');
      }
    });

    return buffer.toString();
  }

  /// 获取简化的设备信息
  static Future<Map<String, String>> getSimpleDeviceInfo() async {
    try {
      return {
        'deviceId': await generateDeviceId(),
        'deviceName': await getDeviceName(),
        'deviceType': getDeviceType(),
        'osVersion': await getOSVersion(),
        'isPhysicalDevice': (await isPhysicalDevice()).toString(),
      };
    } catch (e) {
      return {
        'deviceId': _uuid.v4(),
        'deviceName': 'Unknown Device',
        'deviceType': 'Unknown',
        'osVersion': 'Unknown',
        'isPhysicalDevice': 'true',
      };
    }
  }
}
