/// 应用路由定义
/// 基于ARCHITECTURE.md规范实现的路由管理
library;

/// 应用路由常量
abstract class AppRoutes {
  // 私有构造函数
  AppRoutes._();

  /// 启动页
  static const String splash = '/splash';
  
  /// 引导页
  static const String onboarding = '/onboarding';
  
  /// 登录页
  static const String login = '/login';
  
  /// 注册页
  static const String register = '/register';
  
  /// 忘记密码页
  static const String forgotPassword = '/forgot-password';
  
  /// 重置密码页
  static const String resetPassword = '/reset-password';
  
  /// 首页
  static const String home = '/home';
  
  /// 主页面（底部导航）
  static const String main = '/main';
  
  /// 个人中心
  static const String profile = '/profile';
  
  /// 设置页
  static const String settings = '/settings';
  
  /// 关于页
  static const String about = '/about';
  
  /// 学习页面
  static const String study = '/study';
  
  /// 复习页面
  static const String review = '/review';
  
  /// 书籍列表
  static const String bookList = '/book-list';
  
  /// 书籍详情
  static const String bookDetail = '/book-detail';
  
  /// 单词列表
  static const String wordList = '/word-list';
  
  /// 单词详情
  static const String wordDetail = '/word-detail';
  
  /// 发音练习
  static const String pronunciation = '/pronunciation';
  
  /// 语音识别
  static const String speechRecognition = '/speech-recognition';
  
  /// 学习统计
  static const String statistics = '/statistics';
  
  /// 学习历史
  static const String history = '/history';
  
  /// 收藏夹
  static const String favorites = '/favorites';
  
  /// 搜索页
  static const String search = '/search';
  
  /// 通知页
  static const String notifications = '/notifications';
  
  /// 帮助页
  static const String help = '/help';
  
  /// 反馈页
  static const String feedback = '/feedback';
  
  /// 隐私政策
  static const String privacy = '/privacy';
  
  /// 服务条款
  static const String terms = '/terms';
  
  /// 账户管理
  static const String account = '/account';
  
  /// 修改密码
  static const String changePassword = '/change-password';
  
  /// 绑定手机
  static const String bindPhone = '/bind-phone';
  
  /// 绑定邮箱
  static const String bindEmail = '/bind-email';
  
  /// 实名认证
  static const String verification = '/verification';
  
  /// 安全设置
  static const String security = '/security';
  
  /// 主题设置
  static const String theme = '/theme';
  
  /// 语言设置
  static const String language = '/language';
  
  /// 字体设置
  static const String font = '/font';
  
  /// 音频设置
  static const String audio = '/audio';
  
  /// 学习设置
  static const String studySettings = '/study-settings';
  
  /// 提醒设置
  static const String reminder = '/reminder';
  
  /// 备份恢复
  static const String backup = '/backup';
  
  /// 清理缓存
  static const String cache = '/cache';
  
  /// 检查更新
  static const String update = '/update';
  
  /// 错误页面
  static const String error = '/error';
  
  /// 网络错误页面
  static const String networkError = '/network-error';
  
  /// 维护页面
  static const String maintenance = '/maintenance';
  
  /// WebView页面
  static const String webview = '/webview';
  
  /// 图片预览
  static const String imagePreview = '/image-preview';
  
  /// 视频播放
  static const String videoPlayer = '/video-player';
  
  /// 音频播放
  static const String audioPlayer = '/audio-player';
  
  /// 文件预览
  static const String filePreview = '/file-preview';
  
  /// 二维码扫描
  static const String qrScan = '/qr-scan';
  
  /// 二维码生成
  static const String qrGenerate = '/qr-generate';
  
  /// 地图页面
  static const String map = '/map';
  
  /// 相机页面
  static const String camera = '/camera';
  
  /// 录音页面
  static const String recorder = '/recorder';
  
  /// 文件选择
  static const String filePicker = '/file-picker';
  
  /// 图片选择
  static const String imagePicker = '/image-picker';
  
  /// 联系人选择
  static const String contactPicker = '/contact-picker';
  
  /// 日期选择
  static const String datePicker = '/date-picker';
  
  /// 时间选择
  static const String timePicker = '/time-picker';
  
  /// 颜色选择
  static const String colorPicker = '/color-picker';
  
  /// 字体选择
  static const String fontPicker = '/font-picker';
  
  /// 表情选择
  static const String emojiPicker = '/emoji-picker';
  
  /// 贴纸选择
  static const String stickerPicker = '/sticker-picker';
  
  /// 滤镜选择
  static const String filterPicker = '/filter-picker';
  
  /// 模板选择
  static const String templatePicker = '/template-picker';
  
  /// 背景选择
  static const String backgroundPicker = '/background-picker';
  
  /// 音效选择
  static const String soundPicker = '/sound-picker';
  
  /// 动画选择
  static const String animationPicker = '/animation-picker';
  
  /// 特效选择
  static const String effectPicker = '/effect-picker';
  
  /// 工具箱
  static const String toolbox = '/toolbox';
  
  /// 计算器
  static const String calculator = '/calculator';
  
  /// 日历
  static const String calendar = '/calendar';
  
  /// 便签
  static const String notes = '/notes';
  
  /// 待办事项
  static const String todos = '/todos';
  
  /// 提醒事项
  static const String reminders = '/reminders';
  
  /// 闹钟
  static const String alarms = '/alarms';
  
  /// 秒表
  static const String stopwatch = '/stopwatch';
  
  /// 计时器
  static const String timer = '/timer';
  
  /// 天气
  static const String weather = '/weather';
  
  /// 新闻
  static const String news = '/news';
  
  /// 游戏
  static const String games = '/games';
  
  /// 娱乐
  static const String entertainment = '/entertainment';
  
  /// 工具
  static const String tools = '/tools';
  
  /// 实用工具
  static const String utilities = '/utilities';
  
  /// 开发者选项
  static const String developer = '/developer';
  
  /// 调试页面
  static const String debug = '/debug';
  
  /// 测试页面
  static const String test = '/test';
  
  /// 演示页面
  static const String demo = '/demo';
  
  /// 示例页面
  static const String example = '/example';
  
  /// 原型页面
  static const String prototype = '/prototype';
  
  /// 实验页面
  static const String experiment = '/experiment';
  
  /// 预览页面
  static const String preview = '/preview';
  
  /// 编辑页面
  static const String edit = '/edit';
  
  /// 创建页面
  static const String create = '/create';
  
  /// 导入页面
  static const String import = '/import';
  
  /// 导出页面
  static const String export = '/export';
  
  /// 分享页面
  static const String share = '/share';
  
  /// 打印页面
  static const String print = '/print';
  
  /// 下载页面
  static const String download = '/download';
  
  /// 上传页面
  static const String upload = '/upload';
  
  /// 同步页面
  static const String sync = '/sync';
  
  /// 备份页面
  static const String backupPage = '/backup-page';
  
  /// 恢复页面
  static const String restore = '/restore';
  
  /// 迁移页面
  static const String migration = '/migration';
  
  /// 升级页面
  static const String upgrade = '/upgrade';
  
  /// 降级页面
  static const String downgrade = '/downgrade';
  
  /// 回滚页面
  static const String rollback = '/rollback';
  
  /// 重置页面
  static const String reset = '/reset';
  
  /// 初始化页面
  static const String initialize = '/initialize';
  
  /// 配置页面
  static const String configure = '/configure';
  
  /// 安装页面
  static const String install = '/install';
  
  /// 卸载页面
  static const String uninstall = '/uninstall';
  
  /// 激活页面
  static const String activate = '/activate';
  
  /// 停用页面
  static const String deactivate = '/deactivate';
  
  /// 启用页面
  static const String enable = '/enable';
  
  /// 禁用页面
  static const String disable = '/disable';
  
  /// 锁定页面
  static const String lock = '/lock';
  
  /// 解锁页面
  static const String unlock = '/unlock';
  
  /// 暂停页面
  static const String pause = '/pause';
  
  /// 恢复页面
  static const String resume = '/resume';
  
  /// 开始页面
  static const String start = '/start';
  
  /// 停止页面
  static const String stop = '/stop';
  
  /// 重启页面
  static const String restart = '/restart';
  
  /// 刷新页面
  static const String refresh = '/refresh';
  
  /// 重新加载页面
  static const String reload = '/reload';
  
  /// 清空页面
  static const String clear = '/clear';
  
  /// 删除页面
  static const String delete = '/delete';
  
  /// 移除页面
  static const String remove = '/remove';
  
  /// 添加页面
  static const String add = '/add';
  
  /// 插入页面
  static const String insert = '/insert';
  
  /// 更新页面
  static const String updatePage = '/update-page';
  
  /// 修改页面
  static const String modify = '/modify';
  
  /// 编辑页面2
  static const String editPage = '/edit-page';
  
  /// 保存页面
  static const String save = '/save';
  
  /// 另存为页面
  static const String saveAs = '/save-as';
  
  /// 打开页面
  static const String open = '/open';
  
  /// 关闭页面
  static const String close = '/close';
  
  /// 退出页面
  static const String exit = '/exit';
  
  /// 取消页面
  static const String cancel = '/cancel';
  
  /// 确认页面
  static const String confirm = '/confirm';
  
  /// 提交页面
  static const String submit = '/submit';
  
  /// 发送页面
  static const String send = '/send';
  
  /// 接收页面
  static const String receive = '/receive';
  
  /// 连接页面
  static const String connect = '/connect';
  
  /// 断开页面
  static const String disconnect = '/disconnect';
  
  /// 登录页面2
  static const String loginPage = '/login-page';
  
  /// 登出页面
  static const String logout = '/logout';
  
  /// 注册页面2
  static const String registerPage = '/register-page';
  
  /// 注销页面
  static const String unregister = '/unregister';
}
