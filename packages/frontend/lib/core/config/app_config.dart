/// CheeStack 应用配置
/// 基于ARCHITECTURE.md规范实现的应用配置管理
library;

import 'package:flutter/foundation.dart';

/// 应用配置类
class AppConfig {
  /// 应用名称
  static const String appName = 'CheeStack';
  
  /// 应用版本
  static const String appVersion = '2.0.0';
  
  /// 构建号
  static const int buildNumber = 1;
  
  /// 是否为调试模式
  static bool get isDebug => kDebugMode;
  
  /// 是否为发布模式
  static bool get isRelease => kReleaseMode;
  
  /// 是否为性能测试模式
  static bool get isProfile => kProfileMode;
  
  /// API基础URL
  static String get baseUrl {
    if (isDebug) {
      return 'http://localhost:8000';
    } else {
      return 'https://api.cheestack.com';
    }
  }
  
  /// API版本前缀
  static const String apiPrefix = '/api/v1';
  
  /// 完整的API URL
  static String get apiUrl => '$baseUrl$apiPrefix';
  
  /// WebSocket URL
  static String get wsUrl {
    final protocol = baseUrl.startsWith('https') ? 'wss' : 'ws';
    final host = baseUrl.replaceFirst(RegExp(r'https?://'), '');
    return '$protocol://$host$apiPrefix/ws';
  }
  
  /// 超时配置
  static const Duration connectTimeout = Duration(seconds: 30);
  static const Duration receiveTimeout = Duration(seconds: 30);
  static const Duration sendTimeout = Duration(seconds: 30);
  
  /// 缓存配置
  static const Duration cacheMaxAge = Duration(hours: 1);
  static const int cacheMaxCount = 100;
  
  /// 分页配置
  static const int defaultPageSize = 20;
  static const int maxPageSize = 100;
  
  /// 文件上传配置
  static const int maxFileSize = 10 * 1024 * 1024; // 10MB
  static const List<String> allowedImageTypes = [
    'jpg', 'jpeg', 'png', 'gif', 'webp'
  ];
  static const List<String> allowedAudioTypes = [
    'mp3', 'wav', 'aac', 'm4a'
  ];
  static const List<String> allowedVideoTypes = [
    'mp4', 'avi', 'mov', 'wmv'
  ];
  
  /// 音频配置
  static const Duration audioMaxDuration = Duration(minutes: 10);
  static const int audioSampleRate = 44100;
  static const int audioBitRate = 128000;
  
  /// 学习配置
  static const int defaultStudyNumber = 10;
  static const int defaultReviewNumber = 30;
  static const int maxStudyNumber = 50;
  static const int maxReviewNumber = 100;
  
  /// 主题配置
  static const String defaultTheme = 'light';
  static const String defaultLanguage = 'zh_CN';
  
  /// 存储键名
  static const String keyToken = 'access_token';
  static const String keyRefreshToken = 'refresh_token';
  static const String keyUserInfo = 'user_info';
  static const String keyUserConfig = 'user_config';
  static const String keyTheme = 'theme_mode';
  static const String keyLanguage = 'language';
  static const String keyFirstLaunch = 'first_launch';
  static const String keyDeviceId = 'device_id';
  
  /// 错误码定义
  static const int errorCodeSuccess = 200;
  static const int errorCodeUnauthorized = 401;
  static const int errorCodeForbidden = 403;
  static const int errorCodeNotFound = 404;
  static const int errorCodeValidation = 422;
  static const int errorCodeServerError = 500;
  
  /// 日志配置
  static const String logTag = 'CheeStack';
  static const bool enableHttpLog = true;
  static const bool enablePerformanceLog = true;
  
  /// 性能监控配置
  static const bool enablePerformanceMonitoring = true;
  static const Duration performanceThreshold = Duration(milliseconds: 100);
  
  /// 崩溃报告配置
  static const bool enableCrashReporting = !kDebugMode;
  
  /// 分析配置
  static const bool enableAnalytics = !kDebugMode;
  
  /// 推送通知配置
  static const bool enablePushNotifications = true;
  
  /// 生物识别配置
  static const bool enableBiometrics = true;
  
  /// 安全配置
  static const bool enableCertificatePinning = !kDebugMode;
  static const bool enableRootDetection = !kDebugMode;
  
  /// 获取环境名称
  static String get environmentName {
    if (isDebug) return 'development';
    if (isProfile) return 'staging';
    return 'production';
  }
  
  /// 获取用户代理字符串
  static String get userAgent {
    return '$appName/$appVersion ($environmentName)';
  }
  
  /// 验证API URL是否有效
  static bool isValidApiUrl(String url) {
    try {
      final uri = Uri.parse(url);
      return uri.hasScheme && (uri.scheme == 'http' || uri.scheme == 'https');
    } catch (e) {
      return false;
    }
  }
  
  /// 获取文件类型
  static String getFileType(String fileName) {
    final extension = fileName.split('.').last.toLowerCase();
    
    if (allowedImageTypes.contains(extension)) {
      return 'image';
    } else if (allowedAudioTypes.contains(extension)) {
      return 'audio';
    } else if (allowedVideoTypes.contains(extension)) {
      return 'video';
    } else {
      return 'unknown';
    }
  }
  
  /// 检查文件大小是否合法
  static bool isValidFileSize(int fileSize) {
    return fileSize <= maxFileSize;
  }
  
  /// 格式化文件大小
  static String formatFileSize(int bytes) {
    if (bytes < 1024) {
      return '$bytes B';
    } else if (bytes < 1024 * 1024) {
      return '${(bytes / 1024).toStringAsFixed(1)} KB';
    } else if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    } else {
      return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
    }
  }
}
