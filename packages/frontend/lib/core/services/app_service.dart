/// 应用服务
/// 基于ARCHITECTURE.md规范实现的应用初始化和生命周期管理
library;

import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:device_info_plus/device_info_plus.dart';

import '../config/app_config.dart';
import '../storage/storage_service.dart';
import '../network/dio_client.dart';
import '../../shared/utils/device_utils.dart';

/// 应用服务
class AppService extends GetxService {
  final Logger _logger = Logger();
  
  /// 应用信息
  PackageInfo? _packageInfo;
  
  /// 设备信息
  Map<String, dynamic>? _deviceInfo;
  
  /// 是否首次启动
  bool _isFirstLaunch = false;
  
  /// 获取应用信息
  PackageInfo? get packageInfo => _packageInfo;
  
  /// 获取设备信息
  Map<String, dynamic>? get deviceInfo => _deviceInfo;
  
  /// 是否首次启动
  bool get isFirstLaunch => _isFirstLaunch;

  @override
  Future<void> onInit() async {
    super.onInit();
    await _initializeApp();
  }

  /// 初始化应用
  Future<void> _initializeApp() async {
    try {
      _logger.i('开始初始化应用...');
      
      // 1. 初始化存储服务
      await _initStorage();
      
      // 2. 获取应用信息
      await _loadAppInfo();
      
      // 3. 获取设备信息
      await _loadDeviceInfo();
      
      // 4. 检查首次启动
      await _checkFirstLaunch();
      
      // 5. 初始化网络客户端
      _initNetworkClient();
      
      // 6. 设置系统UI
      _setupSystemUI();
      
      // 7. 设置错误处理
      _setupErrorHandling();
      
      _logger.i('应用初始化完成');
      
    } catch (e, stackTrace) {
      _logger.e('应用初始化失败', error: e, stackTrace: stackTrace);
      rethrow;
    }
  }

  /// 初始化存储服务
  Future<void> _initStorage() async {
    try {
      await StorageService.instance.init();
      _logger.i('存储服务初始化完成');
    } catch (e) {
      _logger.e('存储服务初始化失败', error: e);
      rethrow;
    }
  }

  /// 加载应用信息
  Future<void> _loadAppInfo() async {
    try {
      _packageInfo = await PackageInfo.fromPlatform();
      _logger.i('应用信息加载完成: ${_packageInfo?.appName} v${_packageInfo?.version}');
    } catch (e) {
      _logger.e('加载应用信息失败', error: e);
    }
  }

  /// 加载设备信息
  Future<void> _loadDeviceInfo() async {
    try {
      _deviceInfo = await DeviceUtils.getDeviceInfo();
      _logger.i('设备信息加载完成');
    } catch (e) {
      _logger.e('加载设备信息失败', error: e);
    }
  }

  /// 检查首次启动
  Future<void> _checkFirstLaunch() async {
    try {
      final storage = StorageService.instance;
      final hasLaunched = await storage.getBool(AppConfig.keyFirstLaunch);
      
      _isFirstLaunch = hasLaunched != true;
      
      if (_isFirstLaunch) {
        await storage.setBool(AppConfig.keyFirstLaunch, true);
        _logger.i('检测到首次启动');
      }
    } catch (e) {
      _logger.e('检查首次启动状态失败', error: e);
    }
  }

  /// 初始化网络客户端
  void _initNetworkClient() {
    try {
      // 获取Dio客户端实例，这会触发初始化
      DioClient.instance;
      _logger.i('网络客户端初始化完成');
    } catch (e) {
      _logger.e('网络客户端初始化失败', error: e);
    }
  }

  /// 设置系统UI
  void _setupSystemUI() {
    try {
      // 设置状态栏样式
      SystemChrome.setSystemUIOverlayStyle(
        const SystemUiOverlayStyle(
          statusBarColor: Colors.transparent,
          statusBarIconBrightness: Brightness.dark,
          statusBarBrightness: Brightness.light,
          systemNavigationBarColor: Colors.white,
          systemNavigationBarIconBrightness: Brightness.dark,
        ),
      );
      
      // 设置屏幕方向
      SystemChrome.setPreferredOrientations([
        DeviceOrientation.portraitUp,
        DeviceOrientation.portraitDown,
      ]);
      
      _logger.i('系统UI设置完成');
    } catch (e) {
      _logger.e('系统UI设置失败', error: e);
    }
  }

  /// 设置错误处理
  void _setupErrorHandling() {
    // Flutter错误处理
    FlutterError.onError = (FlutterErrorDetails details) {
      _logger.e(
        'Flutter错误',
        error: details.exception,
        stackTrace: details.stack,
      );
      
      // 在调试模式下显示错误
      if (kDebugMode) {
        FlutterError.presentError(details);
      }
    };
    
    // 异步错误处理
    PlatformDispatcher.instance.onError = (error, stack) {
      _logger.e('异步错误', error: error, stackTrace: stack);
      return true;
    };
    
    _logger.i('错误处理设置完成');
  }

  /// 获取应用版本信息
  String getVersionInfo() {
    if (_packageInfo == null) return 'Unknown';
    return '${_packageInfo!.version}+${_packageInfo!.buildNumber}';
  }

  /// 获取应用名称
  String getAppName() {
    return _packageInfo?.appName ?? AppConfig.appName;
  }

  /// 获取包名
  String getPackageName() {
    return _packageInfo?.packageName ?? 'unknown';
  }

  /// 获取构建号
  String getBuildNumber() {
    return _packageInfo?.buildNumber ?? '0';
  }

  /// 获取设备ID
  Future<String> getDeviceId() async {
    try {
      final storage = StorageService.instance;
      String? deviceId = await storage.getString(AppConfig.keyDeviceId);
      
      if (deviceId == null || deviceId.isEmpty) {
        deviceId = await DeviceUtils.generateDeviceId();
        await storage.setString(AppConfig.keyDeviceId, deviceId);
      }
      
      return deviceId;
    } catch (e) {
      _logger.e('获取设备ID失败', error: e);
      return 'unknown';
    }
  }

  /// 清除应用数据
  Future<void> clearAppData() async {
    try {
      await StorageService.instance.clear();
      await DioClient.instance.clearCache();
      _logger.i('应用数据清除完成');
    } catch (e) {
      _logger.e('清除应用数据失败', error: e);
    }
  }

  /// 重启应用
  Future<void> restartApp() async {
    try {
      // 清除缓存
      await DioClient.instance.clearCache();
      
      // 重新初始化
      await _initializeApp();
      
      // 重新加载页面
      Get.offAllNamed('/');
      
      _logger.i('应用重启完成');
    } catch (e) {
      _logger.e('应用重启失败', error: e);
    }
  }

  /// 检查应用更新
  Future<bool> checkForUpdates() async {
    try {
      // 这里可以实现检查更新的逻辑
      // 比如调用API检查最新版本
      return false;
    } catch (e) {
      _logger.e('检查更新失败', error: e);
      return false;
    }
  }

  /// 获取应用统计信息
  Future<Map<String, dynamic>> getAppStats() async {
    try {
      final storage = StorageService.instance;
      final storageSize = await storage.getStorageSize();
      
      return {
        'app_name': getAppName(),
        'version': getVersionInfo(),
        'package_name': getPackageName(),
        'device_info': _deviceInfo,
        'storage_size': storageSize,
        'is_first_launch': _isFirstLaunch,
        'debug_mode': kDebugMode,
      };
    } catch (e) {
      _logger.e('获取应用统计信息失败', error: e);
      return {};
    }
  }

  @override
  void onClose() {
    _logger.i('应用服务关闭');
    super.onClose();
  }
}
