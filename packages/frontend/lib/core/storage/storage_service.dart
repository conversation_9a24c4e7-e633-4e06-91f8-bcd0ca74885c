/// 本地存储服务
/// 基于ARCHITECTURE.md规范实现的统一存储管理
library;

import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:logger/logger.dart';

/// 本地存储服务单例
class StorageService {
  static StorageService? _instance;
  SharedPreferences? _prefs;
  final Logger _logger = Logger();

  /// 获取单例实例
  static StorageService get instance {
    _instance ??= StorageService._internal();
    return _instance!;
  }

  /// 私有构造函数
  StorageService._internal();

  /// 初始化存储服务
  Future<void> init() async {
    try {
      _prefs = await SharedPreferences.getInstance();
      _logger.i('StorageService initialized successfully');
    } catch (e) {
      _logger.e('Failed to initialize StorageService', error: e);
      rethrow;
    }
  }

  /// 确保已初始化
  void _ensureInitialized() {
    if (_prefs == null) {
      throw StateError('StorageService not initialized. Call init() first.');
    }
  }

  /// 存储字符串
  Future<bool> setString(String key, String value) async {
    try {
      _ensureInitialized();
      final result = await _prefs!.setString(key, value);
      _logger.d('Stored string: $key');
      return result;
    } catch (e) {
      _logger.e('Failed to store string: $key', error: e);
      return false;
    }
  }

  /// 获取字符串
  Future<String?> getString(String key) async {
    try {
      _ensureInitialized();
      final value = _prefs!.getString(key);
      _logger.d('Retrieved string: $key = ${value != null ? '[HIDDEN]' : 'null'}');
      return value;
    } catch (e) {
      _logger.e('Failed to retrieve string: $key', error: e);
      return null;
    }
  }

  /// 存储整数
  Future<bool> setInt(String key, int value) async {
    try {
      _ensureInitialized();
      final result = await _prefs!.setInt(key, value);
      _logger.d('Stored int: $key = $value');
      return result;
    } catch (e) {
      _logger.e('Failed to store int: $key', error: e);
      return false;
    }
  }

  /// 获取整数
  Future<int?> getInt(String key) async {
    try {
      _ensureInitialized();
      final value = _prefs!.getInt(key);
      _logger.d('Retrieved int: $key = $value');
      return value;
    } catch (e) {
      _logger.e('Failed to retrieve int: $key', error: e);
      return null;
    }
  }

  /// 存储双精度浮点数
  Future<bool> setDouble(String key, double value) async {
    try {
      _ensureInitialized();
      final result = await _prefs!.setDouble(key, value);
      _logger.d('Stored double: $key = $value');
      return result;
    } catch (e) {
      _logger.e('Failed to store double: $key', error: e);
      return false;
    }
  }

  /// 获取双精度浮点数
  Future<double?> getDouble(String key) async {
    try {
      _ensureInitialized();
      final value = _prefs!.getDouble(key);
      _logger.d('Retrieved double: $key = $value');
      return value;
    } catch (e) {
      _logger.e('Failed to retrieve double: $key', error: e);
      return null;
    }
  }

  /// 存储布尔值
  Future<bool> setBool(String key, bool value) async {
    try {
      _ensureInitialized();
      final result = await _prefs!.setBool(key, value);
      _logger.d('Stored bool: $key = $value');
      return result;
    } catch (e) {
      _logger.e('Failed to store bool: $key', error: e);
      return false;
    }
  }

  /// 获取布尔值
  Future<bool?> getBool(String key) async {
    try {
      _ensureInitialized();
      final value = _prefs!.getBool(key);
      _logger.d('Retrieved bool: $key = $value');
      return value;
    } catch (e) {
      _logger.e('Failed to retrieve bool: $key', error: e);
      return null;
    }
  }

  /// 存储字符串列表
  Future<bool> setStringList(String key, List<String> value) async {
    try {
      _ensureInitialized();
      final result = await _prefs!.setStringList(key, value);
      _logger.d('Stored string list: $key (${value.length} items)');
      return result;
    } catch (e) {
      _logger.e('Failed to store string list: $key', error: e);
      return false;
    }
  }

  /// 获取字符串列表
  Future<List<String>?> getStringList(String key) async {
    try {
      _ensureInitialized();
      final value = _prefs!.getStringList(key);
      _logger.d('Retrieved string list: $key (${value?.length ?? 0} items)');
      return value;
    } catch (e) {
      _logger.e('Failed to retrieve string list: $key', error: e);
      return null;
    }
  }

  /// 存储JSON对象
  Future<bool> setJson(String key, Map<String, dynamic> value) async {
    try {
      final jsonString = jsonEncode(value);
      return await setString(key, jsonString);
    } catch (e) {
      _logger.e('Failed to store JSON: $key', error: e);
      return false;
    }
  }

  /// 获取JSON对象
  Future<Map<String, dynamic>?> getJson(String key) async {
    try {
      final jsonString = await getString(key);
      if (jsonString == null) return null;
      
      return jsonDecode(jsonString) as Map<String, dynamic>;
    } catch (e) {
      _logger.e('Failed to retrieve JSON: $key', error: e);
      return null;
    }
  }

  /// 存储对象（通过JSON序列化）
  Future<bool> setObject<T>(String key, T object) async {
    try {
      if (object == null) {
        return await remove(key);
      }
      
      final jsonString = jsonEncode(object);
      return await setString(key, jsonString);
    } catch (e) {
      _logger.e('Failed to store object: $key', error: e);
      return false;
    }
  }

  /// 获取对象（通过JSON反序列化）
  Future<T?> getObject<T>(String key, T Function(Map<String, dynamic>) fromJson) async {
    try {
      final jsonString = await getString(key);
      if (jsonString == null) return null;
      
      final jsonMap = jsonDecode(jsonString) as Map<String, dynamic>;
      return fromJson(jsonMap);
    } catch (e) {
      _logger.e('Failed to retrieve object: $key', error: e);
      return null;
    }
  }

  /// 检查键是否存在
  Future<bool> containsKey(String key) async {
    try {
      _ensureInitialized();
      return _prefs!.containsKey(key);
    } catch (e) {
      _logger.e('Failed to check key existence: $key', error: e);
      return false;
    }
  }

  /// 删除指定键
  Future<bool> remove(String key) async {
    try {
      _ensureInitialized();
      final result = await _prefs!.remove(key);
      _logger.d('Removed key: $key');
      return result;
    } catch (e) {
      _logger.e('Failed to remove key: $key', error: e);
      return false;
    }
  }

  /// 删除多个键
  Future<void> removeKeys(List<String> keys) async {
    for (final key in keys) {
      await remove(key);
    }
  }

  /// 清空所有数据
  Future<bool> clear() async {
    try {
      _ensureInitialized();
      final result = await _prefs!.clear();
      _logger.w('Cleared all storage data');
      return result;
    } catch (e) {
      _logger.e('Failed to clear storage', error: e);
      return false;
    }
  }

  /// 获取所有键
  Future<Set<String>> getAllKeys() async {
    try {
      _ensureInitialized();
      return _prefs!.getKeys();
    } catch (e) {
      _logger.e('Failed to get all keys', error: e);
      return <String>{};
    }
  }

  /// 重新加载数据
  Future<void> reload() async {
    try {
      _ensureInitialized();
      await _prefs!.reload();
      _logger.d('Storage data reloaded');
    } catch (e) {
      _logger.e('Failed to reload storage', error: e);
    }
  }

  /// 获取存储大小（估算）
  Future<int> getStorageSize() async {
    try {
      _ensureInitialized();
      final keys = _prefs!.getKeys();
      int totalSize = 0;
      
      for (final key in keys) {
        final value = _prefs!.get(key);
        if (value is String) {
          totalSize += value.length * 2; // UTF-16编码，每个字符2字节
        } else {
          totalSize += value.toString().length * 2;
        }
        totalSize += key.length * 2; // 键名也占用空间
      }
      
      return totalSize;
    } catch (e) {
      _logger.e('Failed to calculate storage size', error: e);
      return 0;
    }
  }

  /// 导出所有数据
  Future<Map<String, dynamic>> exportData() async {
    try {
      _ensureInitialized();
      final keys = _prefs!.getKeys();
      final data = <String, dynamic>{};
      
      for (final key in keys) {
        data[key] = _prefs!.get(key);
      }
      
      _logger.i('Exported ${data.length} storage items');
      return data;
    } catch (e) {
      _logger.e('Failed to export storage data', error: e);
      return {};
    }
  }

  /// 导入数据
  Future<bool> importData(Map<String, dynamic> data) async {
    try {
      _ensureInitialized();
      
      for (final entry in data.entries) {
        final key = entry.key;
        final value = entry.value;
        
        if (value is String) {
          await _prefs!.setString(key, value);
        } else if (value is int) {
          await _prefs!.setInt(key, value);
        } else if (value is double) {
          await _prefs!.setDouble(key, value);
        } else if (value is bool) {
          await _prefs!.setBool(key, value);
        } else if (value is List<String>) {
          await _prefs!.setStringList(key, value);
        }
      }
      
      _logger.i('Imported ${data.length} storage items');
      return true;
    } catch (e) {
      _logger.e('Failed to import storage data', error: e);
      return false;
    }
  }
}
