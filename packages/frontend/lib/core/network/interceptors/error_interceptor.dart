/// 错误处理拦截器
/// 基于ARCHITECTURE.md规范实现的统一错误处理
library;

import 'package:dio/dio.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:logger/logger.dart';

import '../../config/app_config.dart';
import '../exceptions/network_exception.dart';

/// 错误处理拦截器
class ErrorInterceptor extends Interceptor {
  final Logger _logger = Logger();

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    // 记录错误日志
    _logError(err);
    
    // 转换为自定义异常
    final networkException = _convertToNetworkException(err);
    
    // 显示错误提示（可选）
    _showErrorMessage(networkException);
    
    // 传递转换后的异常
    handler.next(DioException(
      requestOptions: err.requestOptions,
      response: err.response,
      type: err.type,
      error: networkException,
      message: networkException.message,
    ));
  }

  /// 记录错误日志
  void _logError(DioException err) {
    final request = err.requestOptions;
    final response = err.response;
    
    _logger.e(
      'HTTP Error: ${err.type.name}',
      error: {
        'url': '${request.method} ${request.uri}',
        'status_code': response?.statusCode,
        'message': err.message,
        'data': response?.data,
        'headers': response?.headers.map,
      },
      stackTrace: err.stackTrace,
    );
  }

  /// 转换为自定义网络异常
  NetworkException _convertToNetworkException(DioException err) {
    switch (err.type) {
      case DioExceptionType.connectionTimeout:
        return NetworkException.connectionTimeout();
        
      case DioExceptionType.sendTimeout:
        return NetworkException.sendTimeout();
        
      case DioExceptionType.receiveTimeout:
        return NetworkException.receiveTimeout();
        
      case DioExceptionType.badResponse:
        return _handleBadResponse(err);
        
      case DioExceptionType.cancel:
        return NetworkException.requestCancelled();
        
      case DioExceptionType.connectionError:
        return NetworkException.noInternetConnection();
        
      case DioExceptionType.badCertificate:
        return NetworkException.badCertificate();
        
      case DioExceptionType.unknown:
      default:
        return NetworkException.unexpectedError(err.message);
    }
  }

  /// 处理HTTP响应错误
  NetworkException _handleBadResponse(DioException err) {
    final response = err.response;
    final statusCode = response?.statusCode ?? 0;
    final data = response?.data;
    
    // 尝试解析服务器返回的错误信息
    String message = '请求失败';
    
    if (data is Map<String, dynamic>) {
      message = data['msg'] ?? data['message'] ?? message;
    } else if (data is String) {
      message = data;
    }
    
    switch (statusCode) {
      case 400:
        return NetworkException.badRequest(message);
        
      case 401:
        return NetworkException.unauthorized(message);
        
      case 403:
        return NetworkException.forbidden(message);
        
      case 404:
        return NetworkException.notFound(message);
        
      case 422:
        return NetworkException.validationError(message, _extractValidationErrors(data));
        
      case 429:
        return NetworkException.tooManyRequests(message);
        
      case 500:
        return NetworkException.internalServerError(message);
        
      case 502:
        return NetworkException.badGateway(message);
        
      case 503:
        return NetworkException.serviceUnavailable(message);
        
      case 504:
        return NetworkException.gatewayTimeout(message);
        
      default:
        if (statusCode >= 400 && statusCode < 500) {
          return NetworkException.clientError(statusCode, message);
        } else if (statusCode >= 500) {
          return NetworkException.serverError(statusCode, message);
        } else {
          return NetworkException.unexpectedError(message);
        }
    }
  }

  /// 提取验证错误信息
  List<String> _extractValidationErrors(dynamic data) {
    final errors = <String>[];
    
    if (data is Map<String, dynamic>) {
      final dataField = data['data'];
      
      if (dataField is List) {
        for (final error in dataField) {
          if (error is Map<String, dynamic>) {
            final msg = error['msg'] ?? error['message'];
            if (msg is String) {
              errors.add(msg);
            }
          } else if (error is String) {
            errors.add(error);
          }
        }
      } else if (dataField is Map<String, dynamic>) {
        dataField.forEach((key, value) {
          if (value is List) {
            errors.addAll(value.cast<String>());
          } else if (value is String) {
            errors.add(value);
          }
        });
      }
    }
    
    return errors;
  }

  /// 显示错误提示
  void _showErrorMessage(NetworkException exception) {
    // 某些错误不需要显示提示
    if (exception.type == NetworkExceptionType.requestCancelled ||
        exception.type == NetworkExceptionType.unauthorized) {
      return;
    }
    
    // 在调试模式下显示详细错误信息
    String message = exception.message;
    if (AppConfig.isDebug && exception.details.isNotEmpty) {
      message += '\n详情: ${exception.details.join(', ')}';
    }
    
    // 显示错误提示
    EasyLoading.showError(
      message,
      duration: const Duration(seconds: 3),
      dismissOnTap: true,
    );
  }
}
