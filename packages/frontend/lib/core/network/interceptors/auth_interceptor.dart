/// 认证拦截器
/// 基于ARCHITECTURE.md规范实现的认证令牌管理
library;

import 'package:dio/dio.dart';
import 'package:get/get.dart' as getx;

import '../../config/app_config.dart';
import '../../storage/storage_service.dart';
// import '../../../features/auth/controllers/auth_controller.dart';

/// 认证拦截器
class AuthInterceptor extends Interceptor {
  final StorageService _storage = StorageService.instance;

  @override
  void onRequest(
      RequestOptions options, RequestInterceptorHandler handler) async {
    // 获取存储的访问令牌
    final token = await _storage.getString(AppConfig.keyToken);

    if (token != null && token.isNotEmpty) {
      options.headers['Authorization'] = 'Bearer $token';
    }

    handler.next(options);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) async {
    // 处理401未授权错误
    if (err.response?.statusCode == 401) {
      await _handleUnauthorized(err, handler);
    } else {
      handler.next(err);
    }
  }

  /// 处理未授权错误
  Future<void> _handleUnauthorized(
    DioException err,
    ErrorInterceptorHandler handler,
  ) async {
    try {
      // 尝试刷新令牌
      final refreshed = await _refreshToken();

      if (refreshed) {
        // 重新发送原始请求
        final response = await _retryRequest(err.requestOptions);
        handler.resolve(response);
      } else {
        // 刷新失败，跳转到登录页
        await _handleTokenRefreshFailure();
        handler.next(err);
      }
    } catch (e) {
      // 刷新令牌过程中出错
      await _handleTokenRefreshFailure();
      handler.next(err);
    }
  }

  /// 刷新访问令牌
  Future<bool> _refreshToken() async {
    try {
      final refreshToken = await _storage.getString(AppConfig.keyRefreshToken);

      if (refreshToken == null || refreshToken.isEmpty) {
        return false;
      }

      // 创建新的Dio实例避免循环调用
      final dio = Dio(BaseOptions(
        baseUrl: AppConfig.apiUrl,
        connectTimeout: AppConfig.connectTimeout,
        receiveTimeout: AppConfig.receiveTimeout,
      ));

      final response = await dio.post(
        '/auth/refresh-token',
        data: {'refresh_token': refreshToken},
      );

      if (response.statusCode == 200 && response.data['code'] == 200) {
        final data = response.data['data'];
        final newAccessToken = data['access_token'] as String;

        // 保存新的访问令牌
        await _storage.setString(AppConfig.keyToken, newAccessToken);

        return true;
      }

      return false;
    } catch (e) {
      return false;
    }
  }

  /// 重新发送请求
  Future<Response> _retryRequest(RequestOptions requestOptions) async {
    // 获取新的访问令牌
    final token = await _storage.getString(AppConfig.keyToken);

    if (token != null) {
      requestOptions.headers['Authorization'] = 'Bearer $token';
    }

    // 创建新的Dio实例重新发送请求
    final dio = Dio(BaseOptions(
      baseUrl: requestOptions.baseUrl,
      connectTimeout: AppConfig.connectTimeout,
      receiveTimeout: AppConfig.receiveTimeout,
    ));

    return await dio.request(
      requestOptions.path,
      data: requestOptions.data,
      queryParameters: requestOptions.queryParameters,
      options: Options(
        method: requestOptions.method,
        headers: requestOptions.headers,
        contentType: requestOptions.contentType,
        responseType: requestOptions.responseType,
        followRedirects: requestOptions.followRedirects,
        maxRedirects: requestOptions.maxRedirects,
        receiveDataWhenStatusError: requestOptions.receiveDataWhenStatusError,
        extra: requestOptions.extra,
      ),
    );
  }

  /// 处理令牌刷新失败
  Future<void> _handleTokenRefreshFailure() async {
    // 清除本地存储的认证信息
    await _storage.remove(AppConfig.keyToken);
    await _storage.remove(AppConfig.keyRefreshToken);
    await _storage.remove(AppConfig.keyUserInfo);

    // 直接跳转到登录页
    getx.Get.offAllNamed('/login');
  }
}
