#!/bin/bash

# CheeStack Flutter 前端构建脚本
set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Flutter环境
check_flutter() {
    log_info "检查Flutter环境..."
    
    if ! command -v flutter &> /dev/null; then
        log_error "Flutter 未安装"
        exit 1
    fi
    
    # 检查Flutter版本
    FLUTTER_VERSION=$(flutter --version | head -n 1 | cut -d ' ' -f 2)
    log_info "Flutter版本: $FLUTTER_VERSION"
    
    # 运行Flutter doctor
    flutter doctor
    
    log_info "Flutter环境检查完成"
}

# 安装依赖
install_dependencies() {
    log_info "安装Flutter依赖..."
    
    cd "$(dirname "$0")/.."
    
    flutter pub get
    
    log_info "依赖安装完成"
}

# 运行测试
run_tests() {
    log_info "运行Flutter测试..."
    
    cd "$(dirname "$0")/.."
    
    flutter test
    
    log_info "测试完成"
}

# 代码分析
analyze_code() {
    log_info "运行代码分析..."
    
    cd "$(dirname "$0")/.."
    
    flutter analyze
    
    log_info "代码分析完成"
}

# 构建Android APK
build_android() {
    log_info "构建Android APK..."
    
    cd "$(dirname "$0")/.."
    
    # 构建APK
    flutter build apk --release
    
    # 构建App Bundle (用于Google Play)
    flutter build appbundle --release
    
    log_info "Android构建完成"
    log_info "APK位置: build/app/outputs/flutter-apk/app-release.apk"
    log_info "AAB位置: build/app/outputs/bundle/release/app-release.aab"
}

# 构建iOS IPA
build_ios() {
    log_info "构建iOS IPA..."
    
    cd "$(dirname "$0")/.."
    
    # 检查是否在macOS上
    if [[ "$OSTYPE" != "darwin"* ]]; then
        log_error "iOS构建需要在macOS上进行"
        exit 1
    fi
    
    # 构建iOS
    flutter build ios --release
    
    log_info "iOS构建完成"
    log_info "请使用Xcode Archive来生成IPA文件"
}

# 构建Web版本
build_web() {
    log_info "构建Web版本..."
    
    cd "$(dirname "$0")/.."
    
    flutter build web --release
    
    log_info "Web构建完成"
    log_info "Web文件位置: build/web/"
}

# 构建Linux版本
build_linux() {
    log_info "构建Linux版本..."
    
    cd "$(dirname "$0")/.."
    
    # 检查是否支持Linux桌面
    if ! flutter config | grep -q "linux.*true"; then
        log_error "Linux桌面支持未启用"
        log_info "运行: flutter config --enable-linux-desktop"
        exit 1
    fi
    
    flutter build linux --release
    
    log_info "Linux构建完成"
    log_info "Linux应用位置: build/linux/x64/release/bundle/"
}

# 构建Windows版本
build_windows() {
    log_info "构建Windows版本..."
    
    cd "$(dirname "$0")/.."
    
    # 检查是否支持Windows桌面
    if ! flutter config | grep -q "windows.*true"; then
        log_error "Windows桌面支持未启用"
        log_info "运行: flutter config --enable-windows-desktop"
        exit 1
    fi
    
    flutter build windows --release
    
    log_info "Windows构建完成"
    log_info "Windows应用位置: build/windows/runner/Release/"
}

# 构建macOS版本
build_macos() {
    log_info "构建macOS版本..."
    
    cd "$(dirname "$0")/.."
    
    # 检查是否在macOS上
    if [[ "$OSTYPE" != "darwin"* ]]; then
        log_error "macOS构建需要在macOS上进行"
        exit 1
    fi
    
    # 检查是否支持macOS桌面
    if ! flutter config | grep -q "macos.*true"; then
        log_error "macOS桌面支持未启用"
        log_info "运行: flutter config --enable-macos-desktop"
        exit 1
    fi
    
    flutter build macos --release
    
    log_info "macOS构建完成"
    log_info "macOS应用位置: build/macos/Build/Products/Release/"
}

# 清理构建文件
clean() {
    log_info "清理构建文件..."
    
    cd "$(dirname "$0")/.."
    
    flutter clean
    flutter pub get
    
    log_info "清理完成"
}

# 主函数
main() {
    case "$1" in
        "check")
            check_flutter
            ;;
        "deps")
            install_dependencies
            ;;
        "test")
            install_dependencies
            run_tests
            ;;
        "analyze")
            install_dependencies
            analyze_code
            ;;
        "android")
            check_flutter
            install_dependencies
            run_tests
            build_android
            ;;
        "ios")
            check_flutter
            install_dependencies
            run_tests
            build_ios
            ;;
        "web")
            check_flutter
            install_dependencies
            run_tests
            build_web
            ;;
        "linux")
            check_flutter
            install_dependencies
            run_tests
            build_linux
            ;;
        "windows")
            check_flutter
            install_dependencies
            run_tests
            build_windows
            ;;
        "macos")
            check_flutter
            install_dependencies
            run_tests
            build_macos
            ;;
        "all")
            check_flutter
            install_dependencies
            run_tests
            analyze_code
            build_android
            build_web
            if [[ "$OSTYPE" == "darwin"* ]]; then
                build_ios
                build_macos
            fi
            if [[ "$OSTYPE" == "linux-gnu"* ]]; then
                build_linux
            fi
            ;;
        "clean")
            clean
            ;;
        *)
            echo "用法: $0 {check|deps|test|analyze|android|ios|web|linux|windows|macos|all|clean}"
            echo "  check   - 检查Flutter环境"
            echo "  deps    - 安装依赖"
            echo "  test    - 运行测试"
            echo "  analyze - 代码分析"
            echo "  android - 构建Android版本"
            echo "  ios     - 构建iOS版本"
            echo "  web     - 构建Web版本"
            echo "  linux   - 构建Linux版本"
            echo "  windows - 构建Windows版本"
            echo "  macos   - 构建macOS版本"
            echo "  all     - 构建所有支持的平台"
            echo "  clean   - 清理构建文件"
            exit 1
            ;;
    esac
}

main "$@"
