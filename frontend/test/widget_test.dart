/// Flutter应用测试
/// 基于ARCHITECTURE.md规范实现的Widget测试
library;

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import 'package:cheestack_frontend/main.dart';

void main() {
  group('CheeStack App Tests', () {
    testWidgets('Error app should display error message',
        (WidgetTester tester) async {
      const errorMessage = 'Test error message';

      // 构建错误应用
      await tester.pumpWidget(const ErrorApp(error: errorMessage));

      // 验证错误信息显示
      expect(find.text('应用启动失败'), findsOneWidget);
      expect(find.text(errorMessage), findsOneWidget);
      expect(find.text('重新启动'), findsOneWidget);
    });

    testWidgets('Error app should display error message',
        (WidgetTester tester) async {
      const errorMessage = 'Test error message';

      // 构建错误应用
      await tester.pumpWidget(const ErrorApp(error: errorMessage));

      // 验证错误信息显示
      expect(find.text('应用启动失败'), findsOneWidget);
      expect(find.text(errorMessage), findsOneWidget);
      expect(find.text('重新启动'), findsOneWidget);
    });

    testWidgets('Not found page should display 404 message',
        (WidgetTester tester) async {
      // 构建404页面
      await tester.pumpWidget(
        MaterialApp(
          home: const NotFoundPage(),
        ),
      );

      // 验证404页面内容
      expect(find.text('页面不存在'), findsAtLeastNWidgets(1));
      expect(find.text('您访问的页面不存在或已被删除'), findsOneWidget);
      expect(find.text('返回首页'), findsOneWidget);
    });
  });

  group('App Configuration Tests', () {
    test('Basic test should pass', () {
      // 基础测试
      expect(1 + 1, equals(2));
      expect('CheeStack'.length, equals(9));
    });
  });
}
