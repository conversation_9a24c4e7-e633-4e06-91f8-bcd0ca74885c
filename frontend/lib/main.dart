/// CheeStack Flutter 应用入口
/// 基于ARCHITECTURE.md规范重构的主应用文件
library;

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';

import 'core/config/app_config.dart';
import 'core/services/app_service.dart';
import 'shared/theme/app_theme.dart';
import 'shared/routes/app_routes.dart';
import 'shared/routes/app_pages.dart';
import 'shared/bindings/initial_binding.dart';
import 'shared/translations/app_translations.dart';

/// 全局日志实例
final Logger logger = Logger(
  printer: PrettyPrinter(
    methodCount: 2,
    errorMethodCount: 8,
    lineLength: 120,
    colors: true,
    printEmojis: true,
    printTime: true,
  ),
);

void main() async {
  // 确保Flutter绑定初始化
  WidgetsFlutterBinding.ensureInitialized();
  
  // 设置系统UI样式
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);
  
  // 设置状态栏样式
  SystemChrome.setSystemUIOverlayStyle(
    const SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarIconBrightness: Brightness.dark,
      statusBarBrightness: Brightness.light,
    ),
  );
  
  try {
    logger.i('开始启动CheeStack应用...');
    
    // 初始化应用服务
    await Get.putAsync(() => AppService().init());
    
    logger.i('应用服务初始化完成，启动UI...');
    
    // 启动应用
    runApp(const CheeStackApp());
    
  } catch (e, stackTrace) {
    logger.e('应用启动失败', error: e, stackTrace: stackTrace);
    
    // 显示错误页面
    runApp(ErrorApp(error: e.toString()));
  }
}

/// CheeStack主应用
class CheeStackApp extends StatelessWidget {
  const CheeStackApp({super.key});

  @override
  Widget build(BuildContext context) {
    return ScreenUtilInit(
      designSize: const Size(375, 812), // iPhone X设计尺寸
      minTextAdapt: true,
      splitScreenMode: true,
      builder: (context, child) {
        return GetMaterialApp(
          // 应用基本信息
          title: AppConfig.appName,
          debugShowCheckedModeBanner: AppConfig.isDebug,
          
          // 主题配置
          theme: AppTheme.lightTheme,
          darkTheme: AppTheme.darkTheme,
          themeMode: ThemeMode.system,
          
          // 国际化配置
          locale: const Locale('zh', 'CN'),
          fallbackLocale: const Locale('en', 'US'),
          translations: AppTranslations(),
          
          // 路由配置
          initialRoute: AppRoutes.splash,
          getPages: AppPages.routes,
          initialBinding: InitialBinding(),
          
          // 未知路由处理
          unknownRoute: GetPage(
            name: '/notfound',
            page: () => const NotFoundPage(),
          ),
          
          // 全局配置
          defaultTransition: Transition.cupertino,
          transitionDuration: const Duration(milliseconds: 300),
          
          // EasyLoading配置
          builder: EasyLoading.init(
            builder: (context, child) {
              // 配置EasyLoading样式
              _configureEasyLoading();
              
              return MediaQuery(
                // 禁用系统字体缩放
                data: MediaQuery.of(context).copyWith(textScaleFactor: 1.0),
                child: child!,
              );
            },
          ),
          
          // 路由观察者
          routingCallback: (routing) {
            logger.d('路由变化: ${routing?.current} -> ${routing?.previous}');
          },
          
          // 日志配置
          logWriterCallback: (text, {bool isError = false}) {
            if (AppConfig.isDebug) {
              if (isError) {
                logger.e('GetX: $text');
              } else {
                logger.d('GetX: $text');
              }
            }
          },
        );
      },
    );
  }

  /// 配置EasyLoading样式
  void _configureEasyLoading() {
    EasyLoading.instance
      ..displayDuration = const Duration(milliseconds: 2000)
      ..indicatorType = EasyLoadingIndicatorType.fadingCircle
      ..loadingStyle = EasyLoadingStyle.custom
      ..indicatorSize = 45.0
      ..radius = 10.0
      ..progressColor = Colors.blue
      ..backgroundColor = Colors.white
      ..indicatorColor = Colors.blue
      ..textColor = Colors.black
      ..maskColor = Colors.black.withOpacity(0.5)
      ..userInteractions = false
      ..dismissOnTap = false
      ..animationStyle = EasyLoadingAnimationStyle.scale;
  }
}

/// 错误应用页面
class ErrorApp extends StatelessWidget {
  final String error;
  
  const ErrorApp({super.key, required this.error});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'CheeStack - Error',
      debugShowCheckedModeBanner: false,
      home: Scaffold(
        backgroundColor: Colors.white,
        body: Center(
          child: Padding(
            padding: const EdgeInsets.all(20.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(
                  Icons.error_outline,
                  size: 80,
                  color: Colors.red,
                ),
                const SizedBox(height: 20),
                const Text(
                  '应用启动失败',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                ),
                const SizedBox(height: 10),
                Text(
                  error,
                  style: const TextStyle(
                    fontSize: 14,
                    color: Colors.black54,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 30),
                ElevatedButton(
                  onPressed: () {
                    // 重启应用
                    SystemNavigator.pop();
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 30,
                      vertical: 12,
                    ),
                  ),
                  child: const Text('重新启动'),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

/// 404页面
class NotFoundPage extends StatelessWidget {
  const NotFoundPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('页面不存在'),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        elevation: 0,
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.search_off,
              size: 80,
              color: Colors.grey,
            ),
            const SizedBox(height: 20),
            const Text(
              '页面不存在',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
            const SizedBox(height: 10),
            const Text(
              '您访问的页面不存在或已被删除',
              style: TextStyle(
                fontSize: 14,
                color: Colors.black54,
              ),
            ),
            const SizedBox(height: 30),
            ElevatedButton(
              onPressed: () {
                Get.offAllNamed(AppRoutes.home);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(
                  horizontal: 30,
                  vertical: 12,
                ),
              ),
              child: const Text('返回首页'),
            ),
          ],
        ),
      ),
    );
  }
}
