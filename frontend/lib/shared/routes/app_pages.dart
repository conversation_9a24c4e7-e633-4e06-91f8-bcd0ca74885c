/// 应用页面配置
/// 基于ARCHITECTURE.md规范实现的页面路由配置
library;

import 'package:get/get.dart';

import 'app_routes.dart';
import '../../features/splash/splash_page.dart';
import '../../features/auth/login_page.dart';
import '../../features/home/<USER>';

/// 应用页面配置
class AppPages {
  // 私有构造函数
  AppPages._();

  /// 初始路由
  static const String initial = AppRoutes.splash;

  /// 所有路由配置
  static final List<GetPage> routes = [
    // 启动页
    GetPage(
      name: AppRoutes.splash,
      page: () => const SplashPage(),
      transition: Transition.fade,
      transitionDuration: const Duration(milliseconds: 300),
    ),

    // 登录页
    GetPage(
      name: AppRoutes.login,
      page: () => const LoginPage(),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 300),
    ),

    // 首页
    GetPage(
      name: AppRoutes.home,
      page: () => const HomePage(),
      transition: Transition.fadeIn,
      transitionDuration: const Duration(milliseconds: 300),
    ),
  ];
}
