/// 网络异常定义
/// 基于ARCHITECTURE.md规范实现的网络异常处理
library;

import 'package:equatable/equatable.dart';

/// 网络异常类型枚举
enum NetworkExceptionType {
  /// 连接超时
  connectionTimeout,
  /// 发送超时
  sendTimeout,
  /// 接收超时
  receiveTimeout,
  /// 请求被取消
  requestCancelled,
  /// 无网络连接
  noInternetConnection,
  /// 证书错误
  badCertificate,
  /// 400 错误请求
  badRequest,
  /// 401 未授权
  unauthorized,
  /// 403 禁止访问
  forbidden,
  /// 404 未找到
  notFound,
  /// 422 验证错误
  validationError,
  /// 429 请求过多
  tooManyRequests,
  /// 500 服务器内部错误
  internalServerError,
  /// 502 网关错误
  badGateway,
  /// 503 服务不可用
  serviceUnavailable,
  /// 504 网关超时
  gatewayTimeout,
  /// 客户端错误 (4xx)
  clientError,
  /// 服务器错误 (5xx)
  serverError,
  /// 未知错误
  unexpectedError,
}

/// 网络异常类
class NetworkException extends Equatable implements Exception {
  /// 异常类型
  final NetworkExceptionType type;
  
  /// 错误消息
  final String message;
  
  /// HTTP状态码
  final int? statusCode;
  
  /// 详细错误信息
  final List<String> details;
  
  /// 原始错误对象
  final dynamic originalError;

  const NetworkException({
    required this.type,
    required this.message,
    this.statusCode,
    this.details = const [],
    this.originalError,
  });

  /// 连接超时
  factory NetworkException.connectionTimeout([String? message]) {
    return NetworkException(
      type: NetworkExceptionType.connectionTimeout,
      message: message ?? '连接超时，请检查网络连接',
    );
  }

  /// 发送超时
  factory NetworkException.sendTimeout([String? message]) {
    return NetworkException(
      type: NetworkExceptionType.sendTimeout,
      message: message ?? '发送请求超时',
    );
  }

  /// 接收超时
  factory NetworkException.receiveTimeout([String? message]) {
    return NetworkException(
      type: NetworkExceptionType.receiveTimeout,
      message: message ?? '接收响应超时',
    );
  }

  /// 请求被取消
  factory NetworkException.requestCancelled([String? message]) {
    return NetworkException(
      type: NetworkExceptionType.requestCancelled,
      message: message ?? '请求已取消',
    );
  }

  /// 无网络连接
  factory NetworkException.noInternetConnection([String? message]) {
    return NetworkException(
      type: NetworkExceptionType.noInternetConnection,
      message: message ?? '无网络连接，请检查网络设置',
    );
  }

  /// 证书错误
  factory NetworkException.badCertificate([String? message]) {
    return NetworkException(
      type: NetworkExceptionType.badCertificate,
      message: message ?? '证书验证失败',
    );
  }

  /// 400 错误请求
  factory NetworkException.badRequest([String? message]) {
    return NetworkException(
      type: NetworkExceptionType.badRequest,
      message: message ?? '请求参数错误',
      statusCode: 400,
    );
  }

  /// 401 未授权
  factory NetworkException.unauthorized([String? message]) {
    return NetworkException(
      type: NetworkExceptionType.unauthorized,
      message: message ?? '未授权访问',
      statusCode: 401,
    );
  }

  /// 403 禁止访问
  factory NetworkException.forbidden([String? message]) {
    return NetworkException(
      type: NetworkExceptionType.forbidden,
      message: message ?? '禁止访问',
      statusCode: 403,
    );
  }

  /// 404 未找到
  factory NetworkException.notFound([String? message]) {
    return NetworkException(
      type: NetworkExceptionType.notFound,
      message: message ?? '请求的资源不存在',
      statusCode: 404,
    );
  }

  /// 422 验证错误
  factory NetworkException.validationError([String? message, List<String>? details]) {
    return NetworkException(
      type: NetworkExceptionType.validationError,
      message: message ?? '数据验证失败',
      statusCode: 422,
      details: details ?? [],
    );
  }

  /// 429 请求过多
  factory NetworkException.tooManyRequests([String? message]) {
    return NetworkException(
      type: NetworkExceptionType.tooManyRequests,
      message: message ?? '请求过于频繁，请稍后再试',
      statusCode: 429,
    );
  }

  /// 500 服务器内部错误
  factory NetworkException.internalServerError([String? message]) {
    return NetworkException(
      type: NetworkExceptionType.internalServerError,
      message: message ?? '服务器内部错误',
      statusCode: 500,
    );
  }

  /// 502 网关错误
  factory NetworkException.badGateway([String? message]) {
    return NetworkException(
      type: NetworkExceptionType.badGateway,
      message: message ?? '网关错误',
      statusCode: 502,
    );
  }

  /// 503 服务不可用
  factory NetworkException.serviceUnavailable([String? message]) {
    return NetworkException(
      type: NetworkExceptionType.serviceUnavailable,
      message: message ?? '服务暂时不可用',
      statusCode: 503,
    );
  }

  /// 504 网关超时
  factory NetworkException.gatewayTimeout([String? message]) {
    return NetworkException(
      type: NetworkExceptionType.gatewayTimeout,
      message: message ?? '网关超时',
      statusCode: 504,
    );
  }

  /// 客户端错误 (4xx)
  factory NetworkException.clientError(int statusCode, [String? message]) {
    return NetworkException(
      type: NetworkExceptionType.clientError,
      message: message ?? '客户端请求错误',
      statusCode: statusCode,
    );
  }

  /// 服务器错误 (5xx)
  factory NetworkException.serverError(int statusCode, [String? message]) {
    return NetworkException(
      type: NetworkExceptionType.serverError,
      message: message ?? '服务器错误',
      statusCode: statusCode,
    );
  }

  /// 未知错误
  factory NetworkException.unexpectedError([String? message, dynamic originalError]) {
    return NetworkException(
      type: NetworkExceptionType.unexpectedError,
      message: message ?? '发生未知错误',
      originalError: originalError,
    );
  }

  /// 是否为网络连接问题
  bool get isConnectionError {
    return type == NetworkExceptionType.connectionTimeout ||
           type == NetworkExceptionType.noInternetConnection ||
           type == NetworkExceptionType.badCertificate;
  }

  /// 是否为超时错误
  bool get isTimeoutError {
    return type == NetworkExceptionType.connectionTimeout ||
           type == NetworkExceptionType.sendTimeout ||
           type == NetworkExceptionType.receiveTimeout ||
           type == NetworkExceptionType.gatewayTimeout;
  }

  /// 是否为认证错误
  bool get isAuthError {
    return type == NetworkExceptionType.unauthorized ||
           type == NetworkExceptionType.forbidden;
  }

  /// 是否为客户端错误
  bool get isClientError {
    return statusCode != null && statusCode! >= 400 && statusCode! < 500;
  }

  /// 是否为服务器错误
  bool get isServerError {
    return statusCode != null && statusCode! >= 500;
  }

  /// 是否可以重试
  bool get canRetry {
    return isTimeoutError || 
           type == NetworkExceptionType.noInternetConnection ||
           type == NetworkExceptionType.serviceUnavailable ||
           type == NetworkExceptionType.badGateway;
  }

  @override
  List<Object?> get props => [type, message, statusCode, details, originalError];

  @override
  String toString() {
    final buffer = StringBuffer('NetworkException: $message');
    
    if (statusCode != null) {
      buffer.write(' (Status: $statusCode)');
    }
    
    if (details.isNotEmpty) {
      buffer.write(' Details: ${details.join(', ')}');
    }
    
    return buffer.toString();
  }
}
