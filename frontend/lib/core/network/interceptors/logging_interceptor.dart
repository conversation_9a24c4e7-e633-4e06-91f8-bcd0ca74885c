/// 日志拦截器
/// 基于ARCHITECTURE.md规范实现的HTTP请求日志记录
library;

import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:logger/logger.dart';

import '../../config/app_config.dart';

/// 日志拦截器
class LoggingInterceptor extends Interceptor {
  final Logger _logger;
  final bool _logRequestBody;
  final bool _logResponseBody;
  final int _maxBodyLength;

  LoggingInterceptor(
    this._logger, {
    bool logRequestBody = true,
    bool logResponseBody = true,
    int maxBodyLength = 1000,
  })  : _logRequestBody = logRequestBody,
        _logResponseBody = logResponseBody,
        _maxBodyLength = maxBodyLength;

  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    if (AppConfig.enableHttpLog) {
      _logRequest(options);
    }
    handler.next(options);
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    if (AppConfig.enableHttpLog) {
      _logResponse(response);
    }
    handler.next(response);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    if (AppConfig.enableHttpLog) {
      _logError(err);
    }
    handler.next(err);
  }

  /// 记录请求日志
  void _logRequest(RequestOptions options) {
    final uri = options.uri;
    final method = options.method.toUpperCase();
    
    final logData = <String, dynamic>{
      'method': method,
      'url': uri.toString(),
      'headers': _filterHeaders(options.headers),
    };

    // 记录查询参数
    if (options.queryParameters.isNotEmpty) {
      logData['query_parameters'] = options.queryParameters;
    }

    // 记录请求体
    if (_logRequestBody && options.data != null) {
      logData['body'] = _formatBody(options.data);
    }

    _logger.i('🚀 HTTP Request', error: logData);
  }

  /// 记录响应日志
  void _logResponse(Response response) {
    final request = response.requestOptions;
    final statusCode = response.statusCode;
    final method = request.method.toUpperCase();
    
    final logData = <String, dynamic>{
      'method': method,
      'url': request.uri.toString(),
      'status_code': statusCode,
      'headers': _filterHeaders(response.headers.map),
    };

    // 记录响应体
    if (_logResponseBody && response.data != null) {
      logData['body'] = _formatBody(response.data);
    }

    // 根据状态码选择日志级别
    if (statusCode != null && statusCode >= 200 && statusCode < 300) {
      _logger.i('✅ HTTP Response', error: logData);
    } else {
      _logger.w('⚠️ HTTP Response', error: logData);
    }
  }

  /// 记录错误日志
  void _logError(DioException err) {
    final request = err.requestOptions;
    final response = err.response;
    final method = request.method.toUpperCase();
    
    final logData = <String, dynamic>{
      'method': method,
      'url': request.uri.toString(),
      'error_type': err.type.name,
      'message': err.message,
    };

    if (response != null) {
      logData['status_code'] = response.statusCode;
      logData['response_headers'] = _filterHeaders(response.headers.map);
      
      if (_logResponseBody && response.data != null) {
        logData['response_body'] = _formatBody(response.data);
      }
    }

    _logger.e('❌ HTTP Error', error: logData, stackTrace: err.stackTrace);
  }

  /// 过滤敏感头信息
  Map<String, dynamic> _filterHeaders(Map<String, dynamic> headers) {
    final filtered = <String, dynamic>{};
    
    headers.forEach((key, value) {
      final lowerKey = key.toLowerCase();
      
      // 过滤敏感信息
      if (_isSensitiveHeader(lowerKey)) {
        filtered[key] = '***';
      } else {
        filtered[key] = value;
      }
    });
    
    return filtered;
  }

  /// 检查是否为敏感头信息
  bool _isSensitiveHeader(String headerName) {
    const sensitiveHeaders = [
      'authorization',
      'cookie',
      'set-cookie',
      'x-api-key',
      'x-auth-token',
      'x-access-token',
    ];
    
    return sensitiveHeaders.contains(headerName);
  }

  /// 格式化请求/响应体
  dynamic _formatBody(dynamic body) {
    if (body == null) return null;
    
    try {
      String bodyString;
      
      if (body is String) {
        bodyString = body;
      } else if (body is Map || body is List) {
        bodyString = jsonEncode(body);
      } else {
        bodyString = body.toString();
      }
      
      // 限制日志长度
      if (bodyString.length > _maxBodyLength) {
        bodyString = '${bodyString.substring(0, _maxBodyLength)}... (truncated)';
      }
      
      // 尝试格式化JSON
      try {
        final decoded = jsonDecode(bodyString);
        return decoded;
      } catch (e) {
        return bodyString;
      }
    } catch (e) {
      return 'Failed to format body: $e';
    }
  }
}
