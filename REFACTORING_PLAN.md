# 项目架构重构实施计划

## 🎯 重构目标

### 主要目标
1. **统一目录结构**: 建立清晰的分层架构
2. **优化依赖管理**: 实现统一的依赖注入模式
3. **建立共享代码包**: 消除前后端重复代码
4. **完善测试体系**: 建立完整的测试金字塔
5. **提升代码质量**: 统一代码规范和质量检查

## 📊 项目现状评估

### ✅ 现有项目优点
- 功能完整，业务逻辑清晰
- 技术栈现代化，性能良好
- 已有一定的模块化结构
- 核心功能稳定运行

### ⚠️ 需要改进的问题
- 目录结构与ARCHITECTURE.md规范差异较大
- 缺乏统一的依赖注入管理
- 测试覆盖率不足
- 前后端数据模型重复定义

## 📋 重构阶段规划

### 阶段一：后端架构重构 (2-3天)

#### 1.1 后端目录重构
```bash
# 当前结构
cheestack-fastapi/
├── apps/
├── core/
└── main.py

# 目标结构
backend/
├── app/
│   ├── core/
│   ├── features/
│   └── main.py
├── tests/
└── migrations/
```

**具体步骤：**
1. 创建新的 `backend/` 目录
2. 重组 `apps/` 为 `features/` 结构
3. 优化 `core/` 模块组织
4. 迁移测试代码到标准位置

#### 1.2 前端目录重构
```bash
# 当前结构
cheestack-flt/lib/
├── apis/
├── controllers/
├── pages/
└── features/

# 目标结构
frontend/lib/
├── core/
├── shared/
├── features/
└── main.dart
```

**具体步骤：**
1. 创建新的 `frontend/` 目录
2. 重组现有功能到 Clean Architecture 结构
3. 统一 GetX 依赖注入模式
4. 优化路由和状态管理

### 阶段二：依赖注入优化 (2-3天)

#### 2.1 后端依赖注入重构
- 引入 `dependency-injector` 库
- 创建统一的容器配置
- 重构现有服务注册方式
- 优化数据库连接管理

#### 2.2 前端依赖注入重构
- 优化 GetX Bindings 结构
- 实现分层的依赖注入
- 改进服务生命周期管理
- 统一错误处理机制

### 阶段三：共享代码包建设 (2-3天)

#### 3.1 创建共享类型定义
- 设计 OpenAPI 规范
- 生成前后端类型定义
- 建立代码生成流程
- 统一数据验证规则

#### 3.2 共享工具函数
- 提取通用工具函数
- 建立共享常量定义
- 统一错误码管理
- 创建共享验证器

### 阶段四：测试架构完善 (2-3天)

#### 4.1 后端测试体系
- 建立单元测试框架
- 创建集成测试套件
- 设置测试数据工厂
- 配置测试覆盖率报告

#### 4.2 前端测试体系
- 建立 Widget 测试
- 创建单元测试套件
- 设置集成测试
- 配置测试自动化

### 阶段五：开发工具配置 (1-2天)

#### 5.1 代码质量工具
- 配置 pre-commit hooks
- 设置代码格式化工具
- 建立 linting 规则
- 配置静态分析

#### 5.2 CI/CD 流程
- 设置 GitHub Actions
- 配置自动化测试
- 建立部署流程
- 设置代码覆盖率检查

## 🔧 具体实施步骤

### 步骤1: 创建新的目录结构

```bash
# 1. 创建新的根目录结构
mkdir -p backend/app/{core,features,shared}
mkdir -p backend/tests/{unit,integration,e2e}
mkdir -p frontend/lib/{core,shared,features}
mkdir -p shared/{types,schemas,utils,codegen}

# 2. 移动现有代码
mv cheestack-fastapi/* backend/
mv cheestack-flt/* frontend/
```

### 步骤2: 重构后端架构

#### 2.1 创建核心基础设施
```python
# backend/app/core/dependencies/container.py
from dependency_injector import containers, providers

class Container(containers.DeclarativeContainer):
    wiring_config = containers.WiringConfiguration(
        modules=["app.features.auth.presentation.routers"]
    )
    
    # 配置提供者
    config = providers.Configuration()
    
    # 数据库提供者
    database = providers.Singleton(
        DatabaseManager,
        database_url=config.database.url
    )
```

#### 2.2 重构功能模块
```python
# backend/app/features/auth/domain/entities/user.py
from dataclasses import dataclass
from typing import Optional

@dataclass
class User:
    id: Optional[int]
    email: str
    username: str
    is_active: bool = True
```

### 步骤3: 重构前端架构

#### 3.1 创建依赖注入配置
```dart
// frontend/lib/core/di/app_binding.dart
class AppBinding extends Bindings {
  @override
  void dependencies() {
    _registerCore();
    _registerDataSources();
    _registerRepositories();
    _registerUseCases();
    _registerControllers();
  }
  
  void _registerCore() {
    Get.lazyPut<HttpClient>(() => HttpClient());
    Get.lazyPut<StorageService>(() => StorageService());
  }
}
```

#### 3.2 实现Clean Architecture
```dart
// frontend/lib/features/auth/domain/entities/user.dart
class User extends Equatable {
  final int id;
  final String email;
  final String username;
  
  const User({
    required this.id,
    required this.email,
    required this.username,
  });
  
  @override
  List<Object> get props => [id, email, username];
}
```

## 📊 重构进度跟踪

### 完成标准
- [ ] 目录结构重组完成
- [ ] 依赖注入优化完成
- [ ] 共享代码包建立
- [ ] 测试覆盖率达到80%+
- [ ] CI/CD流程配置完成
- [ ] 代码质量检查通过

### 风险控制
1. **渐进式重构**: 分模块逐步重构，避免大爆炸式改动
2. **保持向后兼容**: 重构期间保持现有功能正常运行
3. **充分测试**: 每个阶段完成后进行全面测试
4. **文档同步**: 及时更新架构文档和开发指南

## 🎉 重构收益

### 短期收益
- 代码结构更清晰
- 开发效率提升
- 错误减少

### 长期收益
- 维护成本降低
- 新功能开发更快
- 团队协作更顺畅
- 代码质量持续提升
