# 项目架构指南

本文档定义了项目的标准目录结构、代码组织方式和核心设计原则。所有开发者都应遵循此指南，以确保代码的一致性、可维护性和可扩展性。

## 1. 根目录结构 (Monorepo)

项目采用 Monorepo 结构，将所有相关的子项目（如前端、后端、共享库）都放在一个版本库中。

```
/cheestack
├── .vscode/                # VS Code 编辑器配置
│   ├── settings.json
│   ├── launch.json         # 调试配置
│   └── extensions.json     # 推荐扩展
├── backend/                # FastAPI 后端服务
│   ├── app/                # 应用代码
│   │   ├── core/           # 核心配置 (数据库, 设置, 安全)
│   │   ├── features/       # 按功能划分的模块 (e.g., auth, profile)
│   │   └── main.py         # 应用入口
│   ├── tests/              # 测试代码
│   ├── migrations/         # 数据库迁移
│   ├── shared/             # 后端共享代码
│   ├── docker/             # Docker 配置
│   │   ├── Dockerfile
│   │   ├── docker-compose.yml
│   │   └── nginx.conf
│   ├── k3s/                # K3s 部署配置
│   │   ├── namespace.yaml
│   │   ├── configmap.yaml
│   │   ├── secret.yaml
│   │   ├── postgres.yaml
│   │   ├── redis.yaml
│   │   └── backend.yaml
│   ├── scripts/            # 部署脚本
│   │   └── deploy.sh
│   └── pyproject.toml      # Python 项目配置
├── frontend/               # Flutter 前端应用
│   ├── lib/
│   │   ├── core/           # 核心功能 (DI, 路由, 客户端)
│   │   ├── features/       # 按功能划分的模块 (e.g., auth, profile)
│   │   └── shared/         # 前端共享组件和工具
│   ├── test/               # 测试代码
│   ├── scripts/            # 构建脚本
│   │   └── build.sh
│   ├── analysis_options.yaml
│   └── pubspec.yaml
├── docs/                   # 项目文档
│   ├── ARCHITECTURE.md
│   ├── API.md
│   └── DEPLOYMENT.md
│
├── .env.example            # 环境变量模板
├── .gitignore
├── .pre-commit-config.yaml # 代码提交钩子
├── .editorconfig          # 编辑器配置
├── pyproject.toml         # Python 工具配置
└── README.md
```

### 目录说明

*   **`.vscode/`**: 存放项目级的 VS Code 配置，包括调试配置、工作区设置和推荐扩展，确保团队成员拥有一致的开发环境。
*   **`docker/`**: 容器化相关配置，包括各服务的 Dockerfile 和 docker-compose 配置。
*   **`k3s/`**: Kubernetes 部署配置文件，用于生产环境部署。
*   **`scripts/`**: 自动化脚本，包括构建、部署和环境设置脚本。
*   **`packages/`**: 包含所有独立的应用和服务。
    *   **`frontend/`**: Flutter 前端应用。
    *   **`backend/`**: FastAPI 后端应用。
    *   **`shared/`**: 存放前后端共享的代码，确保数据结构和业务逻辑的一致性。

## 2. 后端架构 (FastAPI)

后端服务采用分层架构，使用 Tortoise ORM 作为数据访问层，将不同的职责分离到独立的模块中，并集成完整的错误处理、安全和监控机制。

### 2.1. 目录结构

```
packages/backend/
├── app/
│   ├── core/               # 核心配置和全局服务
│   │   ├── __init__.py
│   │   ├── config.py       # 应用配置 (分层配置: dev/prod/test)
│   │   ├── settings.py     # 环境相关设置
│   │   ├── database.py     # Tortoise ORM 配置和连接管理
│   │   ├── dependencies.py # 统一依赖注入管理 (FastAPI + 应用级DI)
│   │   ├── security.py     # 安全工具 (JWT, 加密, 权限)
│   │   ├── permissions.py  # 权限管理
│   │   ├── exceptions.py   # 自定义异常类
│   │   ├── middleware.py   # 中间件 (CORS, 日志, 异常处理)
│   │   ├── responses.py    # 标准化响应格式
│   │   ├── logging.py      # 结构化日志配置
│   │   ├── metrics.py      # 性能指标收集
│   │   ├── health.py       # 健康检查端点
│   │   └── rate_limiting.py # 限流配置
│   ├── features/           # 按业务功能划分的模块
│   │   ├── __init__.py
│   │   └── auth/           # 认证功能模块
│   │       ├── __init__.py
│   │       ├── router.py   # API 路由 (Presentation Layer)
│   │       ├── service.py  # 业务逻辑 (Application/Service Layer)
│   │       ├── repository.py # 数据访问层 (可选)
│   │       ├── schema.py   # 数据模型/DTO (Data Transfer Objects)
│   │       ├── models.py   # Tortoise ORM 模型 (Data Layer)
│   │       ├── services.py # 数据服务层 (使用 Tortoise ORM)
│   │       └── exceptions.py # 模块特定异常
│   └── main.py             # FastAPI 应用实例和顶层路由
├── migrations/             # Aerich 数据库迁移 (Tortoise ORM)
│   ├── models/             # 迁移模型文件
│   └── 0_initial.py        # 初始迁移文件
├── seeds/                  # 种子数据
│   ├── __init__.py
│   ├── users.py
│   └── run_seeds.py
├── test/                   # 测试代码
│   ├── unit/               # 单元测试
│   │   └── features/
│   │       └── auth/
│   ├── integration/        # 集成测试
│   │   └── test_auth_integration.py
│   ├── e2e/               # 端到端测试
│   │   └── test_user_journey.py
│   ├── fixtures/          # 测试数据
│   │   └── auth_fixtures.py
│   └── conftest.py        # pytest 配置
├── aerich.ini             # Aerich 迁移配置 (Tortoise ORM)
├── pyproject.toml         # 项目依赖和元数据 (PEP 621)
└── README.md
```

### 2.2. 统一依赖注入架构

#### 依赖注入设计原则
*   **单一职责**: 每个依赖只负责一个特定功能
*   **接口隔离**: 通过抽象接口定义依赖契约
*   **依赖反转**: 高层模块不依赖低层模块
*   **生命周期管理**: 单例、请求级、瞬态等不同生命周期

#### 统一依赖管理示例
```python
# app/core/dependencies.py
from functools import lru_cache
from typing import AsyncGenerator, Annotated
from fastapi import Depends, HTTPException, status
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
from tortoise.contrib.fastapi import get_connection

from app.core.database import DatabaseManager
from app.core.settings import get_settings
from app.core.security import JWTManager, PasswordManager
from app.features.auth.repository import AuthRepository
from app.features.auth.service import AuthService

# ============= 应用级依赖 (单例) =============

@lru_cache()
def get_settings():
    """获取应用配置 - 单例"""
    return Settings()

@lru_cache()
def get_jwt_manager() -> JWTManager:
    """获取JWT管理器 - 单例"""
    settings = get_settings()
    return JWTManager(
        secret_key=settings.SECRET_KEY,
        algorithm=settings.ALGORITHM,
        access_token_expire_minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES,
    )

@lru_cache()
def get_password_manager() -> PasswordManager:
    """获取密码管理器 - 单例"""
    return PasswordManager()

@lru_cache()
def get_database_manager() -> DatabaseManager:
    """获取数据库管理器 - 单例"""
    return DatabaseManager()

# ============= 请求级依赖 =============

async def get_database_connection():
    """获取数据库连接 - 请求级"""
    connection = get_connection("default")
    try:
        yield connection
    finally:
        # 连接会被 Tortoise 自动管理
        pass

async def get_auth_repository(
    db_manager: Annotated[DatabaseManager, Depends(get_database_manager)]
) -> AuthRepository:
    """获取认证仓储 - 请求级"""
    return AuthRepository(db_manager)

async def get_auth_service(
    auth_repo: Annotated[AuthRepository, Depends(get_auth_repository)],
    password_manager: Annotated[PasswordManager, Depends(get_password_manager)],
    jwt_manager: Annotated[JWTManager, Depends(get_jwt_manager)]
) -> AuthService:
    """获取认证服务 - 请求级"""
    return AuthService(
        auth_repository=auth_repo,
        password_manager=password_manager,
        jwt_manager=jwt_manager
    )

# ============= 认证和权限依赖 =============

security = HTTPBearer()

async def get_current_user_id(
    credentials: Annotated[HTTPAuthorizationCredentials, Depends(security)],
    jwt_manager: Annotated[JWTManager, Depends(get_jwt_manager)]
) -> int:
    """获取当前用户ID"""
    try:
        payload = jwt_manager.verify_token(credentials.credentials)
        user_id = payload.get("sub")
        if user_id is None:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="无效的认证凭据",
                headers={"WWW-Authenticate": "Bearer"},
            )
        return int(user_id)
    except Exception:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无效的认证凭据",
            headers={"WWW-Authenticate": "Bearer"},
        )

async def get_current_user(
    user_id: Annotated[int, Depends(get_current_user_id)],
    auth_service: Annotated[AuthService, Depends(get_auth_service)]
):
    """获取当前用户完整信息"""
    user = await auth_service.get_user_by_id(user_id)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户不存在"
        )
    return user

async def get_admin_user(
    current_user = Depends(get_current_user)
):
    """获取管理员用户 - 权限检查"""
    if not current_user.is_admin:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="需要管理员权限"
        )
    return current_user

# ============= 工具类依赖 =============

class DependencyProvider:
    """依赖提供者 - 统一管理复杂依赖"""
    
    _instances = {}
    
    @classmethod
    def get_service(cls, service_type: type):
        """获取服务实例"""
        if service_type not in cls._instances:
            cls._instances[service_type] = cls._create_service(service_type)
        return cls._instances[service_type]
    
    @classmethod
    def _create_service(cls, service_type: type):
        """创建服务实例 - 可以根据类型动态创建"""
        # 这里可以实现更复杂的依赖创建逻辑
        if service_type == AuthService:
            return AuthService(
                auth_repository=cls.get_service(AuthRepository),
                password_manager=get_password_manager(),
                jwt_manager=get_jwt_manager()
            )
        # 添加其他服务类型...
        raise ValueError(f"未知的服务类型: {service_type}")

# ============= 验证和中间件依赖 =============

def validate_pagination(
    page: int = 1,
    size: int = 20,
    max_size: int = 100
):
    """分页参数验证"""
    if page < 1:
        raise HTTPException(status_code=400, detail="页码必须大于0")
    if size < 1 or size > max_size:
        raise HTTPException(status_code=400, detail=f"每页大小必须在1-{max_size}之间")
    return {"page": page, "size": size, "offset": (page - 1) * size}

async def rate_limit_check(request):
    """限流检查 - 可以集成 Redis 等"""
    # 实现限流逻辑
    pass
```

#### 在路由中使用依赖注入
```python
# app/features/auth/router.py
from fastapi import APIRouter, Depends
from app.core.dependencies import get_auth_service, get_current_user

router = APIRouter()

@router.post("/login")
async def login(
    login_data: LoginRequest,
    auth_service: AuthService = Depends(get_auth_service)
):
    return await auth_service.login(login_data.email, login_data.password)

@router.get("/profile")
async def get_profile(
    current_user = Depends(get_current_user)
):
    return current_user
```

### 2.2. 分层架构详解

*   **Presentation Layer (`router.py`)**: 负责处理 HTTP 请求和响应，定义 API 路径、参数验证和响应模型，调用 `Service` 层执行业务逻辑，不包含任何业务逻辑。
*   **Application/Service Layer (`service.py`)**: 包含核心的业务逻辑和用例实现，协调多个 `Repository` 或外部服务，处理数据转换和业务规则验证，以及事务管理和业务异常处理。
*   **Data Access Layer (`repository.py`)**: 封装数据访问逻辑，使用 Tortoise ORM 进行数据库操作，负责查询优化和数据库连接管理。
*   **Data Models (`models.py` & `schema.py`)**: `models.py` 定义数据库表结构 (ORM 模型)，`schema.py` 定义 API 数据传输对象 (Pydantic 模型)。

#### Presentation Layer (`router.py`)

#### 安全机制
*   JWT 认证和刷新令牌机制
*   基于角色的访问控制 (RBAC)
*   API 限流和防止暴力破解
*   数据加密和敏感信息保护

#### 中间件栈
*   CORS 配置
*   请求日志记录
*   异常捕获和统一处理
*   性能监控和指标收集
*   请求追踪和链路监控

### 2.4. 安全和中间件配置

1. **请求**: 客户端向 `/auth/login` 发送 POST 请求
2. **中间件**: 请求经过中间件栈处理 (日志、限流等)
3. **路由 (`router.py`)**: `auth_router` 接收并验证请求数据
4. **服务 (`service.py`)**: 调用 `AuthService.login()` 执行业务逻辑
5. **数据访问**: 通过 Tortoise ORM 模型查询用户信息
6. **安全验证**: 验证密码并生成 JWT 令牌
7. **响应**: 返回标准化的 API 响应

### 2.5. 数据流示例 (用户登录)

前端应用遵循 **Clean Architecture** 和 **DDD (Domain-Driven Design)** 原则，使用 **GetX** 作为状态管理解决方案，结合现代组件化开发。


## 3. 后端架构 (Flutter)
### 3.1. 目录结构

```
packages/frontend/
├── lib/
│   ├── core/               # 核心通用功能
│   │   ├── di/             # 依赖注入配置 (GetX)
│   │   │   ├── bindings.dart     # GetX 绑定配置
│   │   │   └── dependencies.dart # 依赖注入设置
│   │   ├── error/          # 错误和异常处理
│   │   │   ├── exceptions.dart
│   │   │   └── failures.dart
│   │   ├── network/        # 网络客户端配置
│   │   │   ├── dio_client.dart
│   │   │   ├── interceptors.dart
│   │   │   └── network_info.dart
│   │   ├── routing/        # 路由配置 (GetX Routing)
│   │   │   ├── app_pages.dart    # GetX 页面路由定义
│   │   │   ├── app_routes.dart   # 路由常量
│   │   │   └── middlewares.dart  # 路由中间件
│   │   ├── theme/          # 主题系统
│   │   │   ├── app_theme.dart
│   │   │   ├── colors.dart
│   │   │   └── typography.dart
│   │   ├── l10n/           # 国际化
│   │   │   ├── app_localizations.dart
│   │   │   └── l10n.yaml
│   │   ├── constants/      # 应用常量
│   │   │   ├── api_constants.dart
│   │   │   └── app_constants.dart
│   │   └── utils/          # 通用工具函数
│   │       ├── validators.dart
│   │       ├── formatters.dart
│   │       └── extensions.dart
│   ├── shared/             # 共享组件和工具
│   │   ├── widgets/        # 通用 UI 组件
│   │   │   ├── buttons/
│   │   │   ├── forms/
│   │   │   ├── loading/
│   │   │   └── dialogs/
│   │   └── utils/          # 共享工具函数
│   │       ├── ui_utils.dart
│   │       └── date_utils.dart
│   ├── features/           # 按功能划分的模块
│   │   └── auth/
│   │       ├── data/
│   │       │   ├── datasources/    # 数据源 (Remote/Local)
│   │       │   │   ├── auth_remote_datasource.dart
│   │       │   │   └── auth_local_datasource.dart
│   │       │   ├── models/         # 数据传输模型 (DTOs)
│   │       │   │   ├── auth_request_model.dart
│   │       │   │   └── auth_response_model.dart
│   │       │   └── repositories/   # Repository 实现
│   │       │       └── auth_repository_impl.dart
│   │       ├── domain/
│   │       │   ├── entities/       # 业务实体
│   │       │   │   └── user_entity.dart
│   │       │   ├── repositories/   # Repository 接口
│   │       │   │   └── auth_repository.dart
│   │       │   └── usecases/       # 业务用例
│   │       │       ├── login_user.dart
│   │       │       ├── logout_user.dart
│   │       │       └── get_current_user.dart
│   │       └── presentation/
│   │           ├── bloc/           # 状态管理 ()
│   │           │   ├── auth_bloc.dart
│   │           │   ├── auth_event.dart
│   │           │   └── auth_state.dart
│   │           ├── providers/      # 简单状态 (Riverpod/Provider)
│   │           │   └── auth_provider.dart
│   │           ├── pages/          # 页面/屏幕
│   │           │   ├── login_page.dart
│   │           │   └── register_page.dart
│   │           └── widgets/        # 功能特定小部件
│   │               ├── login_form.dart
│   │               └── auth_button.dart
│   └── main.dart             # 应用入口
├── test/
│   ├── unit/                 # 单元测试
│   │   └── features/
│   │       └── auth/
│   │           ├── data/
│   │           │   ├── datasources/
│   │           │   ├── models/
│   │           │   └── repositories/
│   │           ├── domain/
│   │           │   └── usecases/
│   │           └── presentation/
│   │               └── bloc/
│   ├── widget/              # Widget 测试
│   │   └── auth/
│   │       └── login_form_test.dart
│   ├── integration/         # 集成测试
│   │   └── auth_flow_test.dart
│   ├── mocks/              # Mock 对象
│   │   ├── mock_auth_repository.dart
│   │   └── mock_auth_datasource.dart
│   └── test_utils.dart     # 测试工具
├── analysis_options.yaml   # Dart 分析选项
└── pubspec.yaml
```

### 3.2. GetX 状态管理架构

#### GetX 核心概念
*   **Controller**: 业务逻辑和状态管理
*   **Binding**: 依赖注入和生命周期管理
*   **GetView**: 响应式 UI 组件
*   **Reactive Variables**: 响应式状态变量

#### GetX 状态分层
*   **GetxController**: 核心控制器，管理复杂业务逻辑
*   **GetxService**: 全局服务，跨页面状态共享
*   **Local State**: 简单的页面内状态使用 Rx 变量

#### 依赖注入策略
```dart
// bindings.dart
class AuthBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut<AuthController>(
      () => AuthController(
        loginUseCase: Get.find(),
        logoutUseCase: Get.find(),
      ),
    );
    
    Get.lazyPut<AuthRepository>(
      () => AuthRepositoryImpl(
        remoteDataSource: Get.find(),
        localDataSource: Get.find(),
      ),
    );
  }
}
```

#### 状态响应式更新
```dart
// auth_controller.dart
class AuthController extends GetxController {
  final _isLoading = false.obs;
  final _user = Rxn<User>();
  final _authStatus = AuthStatus.initial.obs;
  
  bool get isLoading => _isLoading.value;
  User? get user => _user.value;
  AuthStatus get authStatus => _authStatus.value;
  
  Future<void> login(String email, String password) async {
    _isLoading.value = true;
    try {
      final result = await _loginUseCase.call(
        LoginParams(email: email, password: password),
      );
      result.fold(
        (failure) => _handleError(failure),
        (user) => _handleSuccess(user),
      );
    } finally {
      _isLoading.value = false;
    }
  }
}
```

### 3.3. GetX 路由管理

#### 声明式路由配置
```dart
// app_pages.dart
class AppPages {
  static const INITIAL = Routes.SPLASH;
  
  static final routes = [
    GetPage(
      name: Routes.LOGIN,
      page: () => const LoginPage(),
      binding: AuthBinding(),
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: Routes.HOME,
      page: () => const HomePage(),
      binding: HomeBinding(),
      middlewares: [AuthMiddleware()],
    ),
  ];
}

// app_routes.dart
abstract class Routes {
  static const SPLASH = '/splash';
  static const LOGIN = '/login';
  static const HOME = '/home';
  static const PROFILE = '/profile';
}
```

#### 路由中间件
```dart
// middlewares.dart
class AuthMiddleware extends GetMiddleware {
  @override
  RouteSettings? redirect(String? route) {
    final authController = Get.find<AuthController>();
    
    if (!authController.isAuthenticated && route != Routes.LOGIN) {
      return const RouteSettings(name: Routes.LOGIN);
    }
    
    if (authController.isAuthenticated && route == Routes.LOGIN) {
      return const RouteSettings(name: Routes.HOME);
    }
    
    return null;
  }
}
```

### 3.4. 状态持久化和缓存
*   **GetStorage**: 轻量级本地存储解决方案
*   **Hive**: 高性能本地数据库
*   **Secure Storage**: 敏感信息安全存储
*   **Cache Management**: 网络数据智能缓存

### 3.5. UI 组件设计

#### 设计系统
*   统一的主题配置
*   可复用的组件库
*   响应式设计适配
*   无障碍访问支持

#### GetX 组件模式
*   **GetView<T>**: 自动注入控制器的页面组件
*   **GetWidget<T>**: 可复用的响应式组件
*   **GetBuilder**: 手动控制更新的组件
*   **Obx**: 简单响应式组件包装器

```dart
// GetView 示例
class LoginPage extends GetView<AuthController> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Obx(() => controller.isLoading
        ? const Center(child: CircularProgressIndicator())
        : LoginForm(),
      ),
    );
  }
}
```

### 3.6. 数据流示例 (GetX 用户登录)

1. **UI Event**: 用户在 `LoginForm` 中点击登录按钮
2. **Controller Method**: 调用 `AuthController.login()` 方法
3. **Use Case**: 控制器调用 `LoginUserUseCase`
4. **Repository**: 通过 `AuthRepository` 接口调用数据层
5. **Data Source**: `AuthRemoteDataSource` 发送网络请求
6. **Reactive Update**: 使用 `.obs` 变量自动更新状态
7. **UI Rebuild**: `Obx` 组件自动响应状态变化重新构建

## 4. 共享代码包 (`packages/shared`)

为了确保前后端之间的数据一致性和代码复用，所有共享的类型定义、验证规则和工具函数都在此包中定义。

### 4.1. 单一事实来源与代码生成 (Single Source of Truth)

**核心原则**: 项目中所有的数据交换模型（DTOs）和 API 接口定义，其**唯一的“事实来源”是位于 `packages/shared/schemas/` 目录下的 OpenAPI 规范文件 (openapi.yaml)。**

任何数据模型的变更，都必须从修改此 OpenAPI 文件开始，然后通过自动化脚本生成前后端所需的代码。

**工作流程**:
1.  **定义/修改契约**: 在 `openapi.yaml` 中定义或修改 API 路径、请求/响应模型。
2.  **生成代码**: 在根目录运行一个统一的脚本 (例如 `make generate-api` 或 `npm run codegen`)。
3.  **自动化执行**: 该脚本会触发 `openapi-generator` 或类似工具，执行以下操作：
    - 为**后端 (FastAPI)**：生成 Pydantic 模型 (`schema.py`) 到相应的 `features` 目录。
    - 为**前端 (Flutter)**：生成 Dart 数据类 (包含 `fromJson`/`toJson`) 到 `features` 的 `models` 目录。
4.  **使用代码**: 开发人员在各自的代码中直接导入并使用这些自动生成的、类型安全的数据模型。

**禁止行为**: 严禁手动在前端或后端代码中创建或修改 API 数据模型。所有模型必须由代码生成器统一管理。


```
packages/shared/
├── lib/
│   └── src/
│       ├── dtos/             # 数据传输对象
│       │   ├── auth/
│       │   │   ├── login_request_dto.dart
│       │   │   ├── login_response_dto.dart
│       │   │   └── user_dto.dart
│       │   └── common/
│       │       ├── api_response_dto.dart
│       │       └── pagination_dto.dart
│       ├── enums/            # 枚举类型
│       │   ├── user_role.dart
│       │   ├── auth_status.dart
│       │   └── api_status.dart
│       ├── validators/       # 验证规则
│       │   ├── email_validator.dart
│       │   ├── password_validator.dart
│       │   └── common_validators.dart
│       ├── constants/        # 常量定义
│       │   ├── api_constants.dart
│       │   ├── error_codes.dart
│       │   └── app_constants.dart
│       ├── utils/           # 通用工具函数
│       │   ├── date_utils.dart
│       │   ├── string_utils.dart
│       │   └── crypto_utils.dart
│       └── index.dart       # 导出文件
├── codegen/                 # 代码生成配置
│   ├── build.yaml
│   ├── generator_config.yaml
│   └── templates/
│       ├── dto_template.dart
│       └── validator_template.dart
├── schemas/                 # API 模式定义
│   ├── openapi.yaml
│   ├── json_schemas/
│   │   ├── user_schema.json
│   │   └── auth_schema.json
│   └── graphql/
│       └── schema.graphql
├── package.json            # Node.js 包配置
└── pubspec.yaml           # Dart 包配置
```

### 4.1. 代码生成和同步

#### 自动化代码生成
*   使用 `json_serializable` 生成 Dart 序列化代码
*   使用 `quicktype` 生成多语言类型定义
*   使用 `openapi-generator` 生成 API 客户端代码

#### 类型安全保证
*   编译时类型检查
*   运行时数据验证
*   API 契约测试

#### 版本管理
*   语义化版本控制
*   向后兼容性检查
*   自动化版本发布

### 4.2. 使用示例

```dart
// 前端使用
import 'package:shared/shared.dart';

final loginRequest = LoginRequestDto(
  email: email,
  password: password,
);

// 后端使用 (Python)
from shared.dtos.auth import LoginRequestDto

login_request = LoginRequestDto.from_dict(request_data)
```

## 5. 测试策略

### 5.1. 测试金字塔

#### 单元测试 (70%)
*   业务逻辑测试
*   工具函数测试
*   数据模型测试
*   用例测试

#### 集成测试 (20%)
*   API 端点测试
*   数据库操作测试
*   外部服务集成测试
*   组件间交互测试

#### 端到端测试 (10%)
*   完整用户流程测试
*   跨服务测试
*   UI 自动化测试
*   性能测试

### 5.2. 测试工具和框架

#### 后端测试
*   **pytest**: 测试框架
*   **pytest-asyncio**: 异步测试支持
*   **httpx**: HTTP 客户端测试
*   **factory_boy**: 测试数据生成
*   **pytest-cov**: 覆盖率报告

#### 前端测试
*   **flutter_test**: Flutter 测试框架
*   **mockito**: Mock 对象生成
*   **get_test**: GetX 控制器测试工具
*   **integration_test**: 集成测试支持
*   **golden_toolkit**: 黄金测试

### 5.3. 持续集成测试

```yaml
# .github/workflows/test.yml
name: Test

on: [push, pull_request]

jobs:
  backend-test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.11'
      - name: Install dependencies
        run: |
          cd packages/backend
          pip install -e .[test]
      - name: Run tests
        run: |
          cd packages/backend
          pytest --cov=app --cov-report=xml
      - name: Upload coverage
        uses: codecov/codecov-action@v3

  frontend-test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Set up Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: '3.16.0'
      - name: Install dependencies
        run: |
          cd packages/frontend
          flutter pub get
      - name: Run tests
        run: |
          cd packages/frontend
          flutter test --coverage
      - name: Upload coverage
        uses: codecov/codecov-action@v3
```

## 6. 部署和 DevOps

### 6.1. 容器化配置

#### 后端 Dockerfile
```dockerfile
# docker/Dockerfile.backend
FROM python:3.11-slim

WORKDIR /app

COPY packages/backend/pyproject.toml .
RUN pip install -e .

COPY packages/backend/app ./app
COPY packages/shared ./shared

EXPOSE 8000

CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
```

#### 前端 Dockerfile
```dockerfile
# docker/Dockerfile.frontend
FROM nginx:alpine

COPY packages/frontend/build/web /usr/share/nginx/html
COPY docker/nginx.conf /etc/nginx/nginx.conf

EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]
```

### 6.2. Kubernetes 部署

```yaml
# k8s/backend/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: backend-deployment
spec:
  replicas: 3
  selector:
    matchLabels:
      app: backend
  template:
    metadata:
      labels:
        app: backend
    spec:
      containers:
      - name: backend
        image: cheestack/backend:latest
        ports:
        - containerPort: 8000
        env:
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: app-secrets
              key: database-url
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
```

### 6.3. 监控和日志

#### 监控栈
*   **Prometheus**: 指标收集
*   **Grafana**: 可视化面板
*   **AlertManager**: 告警管理
*   **Jaeger**: 分布式追踪

#### 日志管理
*   **ELK Stack**: Elasticsearch, Logstash, Kibana
*   **Fluentd**: 日志收集代理
*   结构化日志格式
*   日志等级管理

## 7. 开发工具和代码质量

### 7.1. 代码质量工具

#### Pre-commit 配置
```yaml
# .pre-commit-config.yaml
repos:
  - repo: https://github.com/psf/black
    rev: 23.3.0
    hooks:
      - id: black
        language_version: python3.11

  - repo: https://github.com/pycqa/isort
    rev: 5.12.0
    hooks:
      - id: isort

  - repo: https://github.com/pycqa/flake8
    rev: 6.0.0
    hooks:
      - id: flake8

  - repo: local
    hooks:
      - id: dart-format
        name: dart format
        entry: dart format
        language: system
        files: \.dart$
```

#### 代码分析配置
```yaml
# analysis_options.yaml
include: package:flutter_lints/flutter.yaml

analyzer:
  exclude:
    - "**/*.g.dart"
    - "**/*.freezed.dart"
  strong-mode:
    implicit-casts: false
    implicit-dynamic: false

linter:
  rules:
    prefer_single_quotes: true
    sort_constructors_first: true
    sort_unnamed_constructors_first: true
    always_use_package_imports: true
```

### 7.2. IDE 配置

#### VS Code 设置
```json
{
  ".vscode/settings.json": {
    "python.defaultInterpreterPath": "./packages/backend/.venv/bin/python",
    "python.formatting.provider": "black",
    "python.linting.enabled": true,
    "python.linting.flake8Enabled": true,
    "dart.flutterSdkPath": "/path/to/flutter",
    "editor.formatOnSave": true,
    "editor.codeActionsOnSave": {
      "source.organizeImports": true
    }
  }
}
```

## 8. 安全最佳实践

### 8.1. 认证和授权

#### JWT 配置
*   访问令牌短期有效期（15分钟）
*   刷新令牌长期有效期（7天）
*   令牌轮换机制
*   安全的令牌存储

#### 权限管理
*   基于角色的访问控制 (RBAC)
*   细粒度权限定义
*   权限缓存机制
*   审计日志记录

### 8.2. 数据保护

#### 敏感数据处理
*   密码哈希 (bcrypt)
*   数据加密 (AES-256)
*   PII 数据脱敏
*   GDPR 合规性

#### API 安全
*   输入验证和清理
*   SQL 注入防护
*   XSS 攻击防护
*   CSRF 保护

### 8.3. 网络安全

*   HTTPS 强制使用
*   CORS 策略配置
*   API 限流和防 DDoS
*   安全头部配置
*   IP 白名单机制

## 9. 性能优化

### 9.1. 后端优化

#### 数据库配置和连接管理 (Tortoise ORM)
```python
# app/core/database.py
from tortoise import Tortoise
from tortoise.contrib.fastapi import register_tortoise
from app.core.settings import get_settings

settings = get_settings()

TORTOISE_ORM = {
    "connections": {
        "default": {
            "engine": "tortoise.backends.asyncpg",
            "credentials": {
                "host": settings.DATABASE_HOST,
                "port": settings.DATABASE_PORT,
                "user": settings.DATABASE_USER,
                "password": settings.DATABASE_PASSWORD,
                "database": settings.DATABASE_NAME,
            }
        }
    },
    "apps": {
        "models": {
            "models": [
                "app.features.auth.models",
                "app.features.user.models",
                "aerich.models"
            ],
            "default_connection": "default",
        }
    },
}

async def init_database():
    """初始化数据库连接"""
    await Tortoise.init(config=TORTOISE_ORM)
    
async def generate_schemas():
    """生成数据库表结构"""
    await Tortoise.generate_schemas()

async def close_database():
    """关闭数据库连接"""
    await Tortoise.close_connections()

def register_database(app):
    """注册数据库到 FastAPI 应用"""
    register_tortoise(
        app,
        config=TORTOISE_ORM,
        generate_schemas=settings.DEBUG,  # 仅在开发环境自动生成表
        add_exception_handlers=True,
    )
```

#### 数据库工具函数
```python
# app/core/db_utils.py (可选的数据库工具文件)
from tortoise.transactions import in_transaction
from typing import Optional, Dict, Any
from tortoise.models import Model

async def get_or_create(model_class: Model, defaults: Optional[Dict[str, Any]] = None, **kwargs):
    """获取或创建模型实例"""
    try:
        instance = await model_class.get(**kwargs)
        return instance, False
    except model_class.DoesNotExist:
        create_kwargs = {**kwargs, **(defaults or {})}
        instance = await model_class.create(**create_kwargs)
        return instance, True

async def bulk_create_or_update(model_class: Model, data_list: list, update_fields: list = None):
    """批量创建或更新"""
    async with in_transaction() as conn:
        # 批量操作逻辑
        pass

class DatabaseManager:
    """数据库管理器 - 处理连接池、事务等"""
    
    @staticmethod
    async def health_check() -> bool:
        """数据库健康检查"""
        try:
            await Tortoise.get_connection("default").execute_query("SELECT 1")
            return True
        except Exception:
            return False
    
    @staticmethod
    async def get_connection_info():
        """获取连接信息"""
        conn = Tortoise.get_connection("default")
        return {
            "engine": conn.__class__.__name__,
            "database": conn.database,
        }
```

```python
# app/main.py - 应用启动时的数据库初始化
from fastapi import FastAPI
from app.core.database import register_database, close_database

app = FastAPI()

# 注册数据库
register_database(app)

@app.on_event("shutdown")
async def shutdown_event():
    await close_database()
```

### 职责分工说明

**`database.py`** - 数据库配置和连接管理：
- Tortoise ORM 配置定义
- 数据库连接初始化和关闭
- FastAPI 应用注册
- 连接池管理

**`db_utils.py`** (可选) - 数据库工具函数：
- 通用数据库操作工具
- 事务管理辅助函数
- 数据库健康检查
- 批量操作工具

**`models.py`** (在各 feature 中) - 数据模型定义：
- Tortoise ORM 模型类
- 字段定义和约束
- 模型方法和属性
- 关系定义
#### 迁移管理 (Aerich)
```bash
# 初始化迁移
aerich init -t app.core.database.TORTOISE_ORM

# 生成迁移文件
aerich migrate --name "add_user_table"

# 执行迁移
aerich upgrade

# 回滚迁移
aerich downgrade
```

#### API 性能
*   响应压缩 (gzip)
*   分页机制
*   异步处理
*   CDN 配置

### 9.2. 前端优化

#### GetX 性能优化
*   **GetX Controller**: 智能依赖管理和内存回收
*   **LazyPut**: 懒加载依赖注入，减少启动时间
*   **GetBuilder**: 精确控制 UI 更新范围
*   **Obx vs GetBuilder**: 根据场景选择合适的响应式方案

```dart
// 性能优化示例
class AuthController extends GetxController {
  // 使用 Worker 优化复杂逻辑
  @override
  void onInit() {
    super.onInit();
    
    // 防抖处理搜索
    debounce(_searchQuery, _performSearch, time: Duration(milliseconds: 500));
    
    // 监听用户状态变化
    ever(_user, _onUserChanged);
  }
  
  // 使用 GetBuilder 优化特定区域更新
  void updateSpecificArea() {
    update(['user_info']); // 只更新特定 ID 的组件
  }
}
```

#### 用户体验
*   骨架屏加载
*   错误边界处理
*   离线支持
*   渐进式 Web 应用 (PWA)

## 10. 文档和协作

### 10.1. 文档标准

#### API 文档
*   OpenAPI/Swagger 规范
*   自动化文档生成
*   示例代码和用例
*   版本控制和变更日志
