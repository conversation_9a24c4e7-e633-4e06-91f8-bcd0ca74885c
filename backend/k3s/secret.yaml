apiVersion: v1
kind: Secret
metadata:
  name: cheestack-secret
  namespace: cheestack
type: Opaque
data:
  # 注意：这些值需要base64编码
  # echo -n "your-secret" | base64
  DATABASE_USER: Y2hlZXN0YWNr  # cheestack
  DATABASE_PASSWORD: Y2hlZXN0YWNrMTIz  # cheestack123
  REDIS_PASSWORD: cmVkaXMxMjM=  # redis123
  JWT_SECRET_KEY: MDlkMjVlMDk0YmFhNmNhMjU1NmM4MTgxNjZiN2E5NTYzYjgzZjcwOTlmNmYwZjRjYWE2Y2Y2M2I4OGU4ZDNlNw==
  SECRET_KEY: eW91ci1zZWNyZXQta2V5LWhlcmU=  # your-secret-key-here
