# CheeStack 后端服务

基于 FastAPI 和 Tortoise ORM 构建的智能学习平台后端服务，采用现代化的分层架构设计。

## 🚀 特性

- **现代化架构**: 基于 FastAPI + Tortoise ORM + PostgreSQL
- **分层设计**: 清晰的核心层、功能层、数据层分离
- **统一响应**: 标准化的 API 响应格式
- **完善的认证**: JWT + 权限管理 + 设备管理
- **异常处理**: 统一的异常处理和错误响应
- **依赖注入**: 基于 FastAPI 的依赖注入系统
- **数据验证**: Pydantic 模型验证
- **API 文档**: 自动生成的 OpenAPI 文档

## 📁 项目结构

```
packages/backend/
├── app/                    # 应用主目录
│   ├── core/              # 核心模块
│   │   ├── database.py    # 数据库配置
│   │   ├── dependencies.py # 依赖注入
│   │   ├── exceptions.py  # 异常处理
│   │   ├── middleware.py  # 中间件
│   │   ├── responses.py   # 响应处理
│   │   ├── security.py    # 安全工具
│   │   ├── settings.py    # 配置管理
│   │   └── routers.py     # 路由管理
│   ├── features/          # 功能模块
│   │   └── auth/          # 认证模块
│   │       ├── models.py  # 数据模型
│   │       ├── schema.py  # 数据验证
│   │       ├── service.py # 业务逻辑
│   │       └── router.py  # 路由定义
│   └── main.py           # 应用入口
├── tests/                # 测试目录
├── migrations/           # 数据库迁移
├── pyproject.toml       # 项目配置
├── .env.example         # 环境配置示例
└── README.md           # 项目说明
```

## 🛠️ 安装和运行

### 环境要求

- Python 3.11+
- PostgreSQL 12+
- Redis 6+

### 安装依赖

```bash
# 进入后端目录
cd packages/backend

# 安装依赖
pip install -e .

# 安装开发依赖
pip install -e ".[dev]"
```

### 配置环境

```bash
# 复制环境配置文件
cp .env.example .env

# 编辑配置文件
vim .env
```

### 数据库迁移

```bash
# 初始化迁移
aerich init -t app.core.database.TORTOISE_ORM

# 生成迁移文件
aerich init-db

# 执行迁移
aerich upgrade
```

### 启动服务

```bash
# 开发模式
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

# 生产模式
uvicorn app.main:app --host 0.0.0.0 --port 8000 --workers 4
```

## 📚 API 文档

启动服务后，访问以下地址查看 API 文档：

- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

## 🧪 测试

```bash
# 运行所有测试
pytest

# 运行测试并生成覆盖率报告
pytest --cov=app --cov-report=html

# 运行特定测试
pytest tests/test_auth.py
```

## 🔧 开发工具

### 代码格式化

```bash
# 格式化代码
black app/
isort app/

# 检查代码质量
flake8 app/
mypy app/
```

### 预提交钩子

```bash
# 安装预提交钩子
pre-commit install

# 手动运行检查
pre-commit run --all-files
```

## 📦 部署

### Docker 部署

```bash
# 构建镜像
docker build -t cheestack-backend .

# 运行容器
docker run -d -p 8000:8000 --env-file .env cheestack-backend
```

### 使用 Docker Compose

```bash
# 启动所有服务
docker-compose up -d

# 查看日志
docker-compose logs -f backend
```

## 🔐 安全

- JWT 令牌认证
- 密码加密存储
- 请求限流
- CORS 配置
- 安全头设置
- SQL 注入防护

## 📈 监控

- 健康检查端点: `/health`
- 详细健康检查: `/health/detailed`
- 系统指标: `/health/metrics`
- 请求追踪和日志记录

## 🤝 贡献

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 支持

如有问题或建议，请：

- 提交 [Issue](https://github.com/cheestack/cheestack/issues)
- 发送邮件至 <EMAIL>
- 查看 [文档](https://docs.cheestack.com)
