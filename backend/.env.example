# CheeStack 后端环境配置示例文件
# 复制此文件为 .env 并修改相应配置

# 应用配置
APP_NAME=CheeStack API
APP_VERSION=2.0.0
DEBUG=true

# 数据库配置
DATABASE_HOST=127.0.0.1
DATABASE_PORT=5432
DATABASE_USER=lyuscott
DATABASE_PASSWORD=test1234
DATABASE_NAME=cheestack

# Redis配置
REDIS_HOST=127.0.0.1
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# JWT配置
JWT_SECRET_KEY=09d25e094baa6ca2556c818166b7a9563b83f7099f6f0f4caa6cf63b88e8d3e7
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=525600

# 安全配置
SECRET_KEY=your-secret-key-here

# CORS配置
CORS_ORIGINS=["*"]
CORS_ALLOW_CREDENTIALS=true
CORS_ALLOW_METHODS=["*"]
CORS_ALLOW_HEADERS=["*"]

# 腾讯云配置
TENCENT_SECRET_ID=
TENCENT_SECRET_KEY=
TENCENT_COS_REGION=
TENCENT_COS_BUCKET=

# 阿里云配置
ALIYUN_ACCESS_KEY_ID=
ALIYUN_ACCESS_KEY_SECRET=
ALIYUN_SMS_SIGN_NAME=
ALIYUN_SMS_TEMPLATE_CODE=

# 日志配置
LOG_LEVEL=INFO
LOG_FORMAT=%(asctime)s - %(name)s - %(levelname)s - %(message)s

# API配置
API_V1_PREFIX=/api/v1

# 限流配置
RATE_LIMIT_ENABLED=true
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_PERIOD=60
