"""
认证模块测试
基于ARCHITECTURE.md规范实现的认证功能测试
"""
import pytest
from fastapi.testclient import TestClient
from app.main import create_app
from app.core.security import Pass<PERSON><PERSON><PERSON><PERSON>, JWTManager
from app.core.settings import get_settings


@pytest.fixture
def client():
    """创建测试客户端"""
    app = create_app()
    return TestClient(app)


@pytest.fixture
def password_manager():
    """创建密码管理器"""
    return PasswordManager()


@pytest.fixture
def jwt_manager():
    """创建JWT管理器"""
    settings = get_settings()
    return JWTManager(
        secret_key=settings.JWT_SECRET_KEY,
        algorithm=settings.JWT_ALGORITHM,
        access_token_expire_minutes=settings.JWT_ACCESS_TOKEN_EXPIRE_MINUTES,
    )


class TestPasswordManager:
    """密码管理器测试"""
    
    def test_hash_password(self, password_manager):
        """测试密码加密"""
        password = "test123456"
        hashed = password_manager.hash_password(password)
        
        assert hashed != password
        assert len(hashed) > 0
        assert hashed.startswith("$2b$")
    
    def test_verify_password(self, password_manager):
        """测试密码验证"""
        password = "test123456"
        hashed = password_manager.hash_password(password)
        
        # 正确密码应该验证成功
        assert password_manager.verify_password(password, hashed) is True
        
        # 错误密码应该验证失败
        assert password_manager.verify_password("wrong_password", hashed) is False
    
    def test_different_passwords_different_hashes(self, password_manager):
        """测试不同密码生成不同哈希"""
        password1 = "test123456"
        password2 = "test654321"
        
        hash1 = password_manager.hash_password(password1)
        hash2 = password_manager.hash_password(password2)
        
        assert hash1 != hash2
    
    def test_same_password_different_hashes(self, password_manager):
        """测试相同密码生成不同哈希（盐值不同）"""
        password = "test123456"
        
        hash1 = password_manager.hash_password(password)
        hash2 = password_manager.hash_password(password)
        
        # 由于盐值不同，哈希应该不同
        assert hash1 != hash2
        
        # 但都应该能验证成功
        assert password_manager.verify_password(password, hash1) is True
        assert password_manager.verify_password(password, hash2) is True


class TestJWTManager:
    """JWT管理器测试"""
    
    def test_create_access_token(self, jwt_manager):
        """测试创建访问令牌"""
        data = {"sub": "test_user_id"}
        token = jwt_manager.create_access_token(data)
        
        assert isinstance(token, str)
        assert len(token) > 0
        assert "." in token  # JWT格式包含点分隔符
    
    def test_verify_token(self, jwt_manager):
        """测试验证令牌"""
        data = {"sub": "test_user_id", "role": "user"}
        token = jwt_manager.create_access_token(data)
        
        payload = jwt_manager.verify_token(token)
        
        assert payload["sub"] == "test_user_id"
        assert payload["role"] == "user"
        assert "exp" in payload
    
    def test_verify_invalid_token(self, jwt_manager):
        """测试验证无效令牌"""
        invalid_token = "invalid.token.here"
        
        with pytest.raises(ValueError, match="无效的Token"):
            jwt_manager.verify_token(invalid_token)
    
    def test_create_refresh_token(self, jwt_manager):
        """测试创建刷新令牌"""
        data = {"sub": "test_user_id"}
        token = jwt_manager.create_refresh_token(data)
        
        assert isinstance(token, str)
        assert len(token) > 0
        
        # 验证刷新令牌包含type字段
        payload = jwt_manager.verify_token(token)
        assert payload["type"] == "refresh"
        assert payload["sub"] == "test_user_id"
    
    def test_token_expiration(self, jwt_manager):
        """测试令牌过期"""
        from datetime import timedelta
        
        # 创建一个立即过期的令牌
        data = {"sub": "test_user_id"}
        token = jwt_manager.create_access_token(data, expires_delta=timedelta(seconds=-1))
        
        # 验证过期令牌应该抛出异常
        with pytest.raises(ValueError, match="Token已过期"):
            jwt_manager.verify_token(token)


class TestAuthAPI:
    """认证API测试"""
    
    def test_sms_code_endpoint(self, client):
        """测试发送短信验证码接口"""
        data = {
            "mobile": "13800138000",
            "code_type": "register"
        }
        
        response = client.post("/api/v1/auth/sms-code", json=data)
        assert response.status_code == 200
        
        result = response.json()
        assert result["code"] == 200
        assert result["msg"] == "验证码发送成功"
    
    def test_sms_code_invalid_mobile(self, client):
        """测试无效手机号"""
        data = {
            "mobile": "invalid_mobile",
            "code_type": "register"
        }
        
        response = client.post("/api/v1/auth/sms-code", json=data)
        assert response.status_code == 422  # 验证错误
    
    def test_sms_code_invalid_type(self, client):
        """测试无效验证码类型"""
        data = {
            "mobile": "13800138000",
            "code_type": "invalid_type"
        }
        
        response = client.post("/api/v1/auth/sms-code", json=data)
        assert response.status_code == 422  # 验证错误
    
    def test_login_missing_credentials(self, client):
        """测试缺少登录凭据"""
        response = client.post("/api/v1/auth/login", json={})
        assert response.status_code == 422  # 验证错误
    
    def test_register_missing_data(self, client):
        """测试缺少注册数据"""
        response = client.post("/api/v1/auth/register", json={})
        assert response.status_code == 422  # 验证错误
    
    def test_register_invalid_mobile(self, client):
        """测试无效手机号注册"""
        data = {
            "mobile": "invalid_mobile",
            "password": "test123456",
            "verification_code": "123456"
        }
        
        response = client.post("/api/v1/auth/register", json=data)
        assert response.status_code == 422  # 验证错误
    
    def test_register_weak_password(self, client):
        """测试弱密码注册"""
        data = {
            "mobile": "13800138000",
            "password": "123",  # 太短的密码
            "verification_code": "123456"
        }
        
        response = client.post("/api/v1/auth/register", json=data)
        assert response.status_code == 422  # 验证错误
    
    def test_unauthorized_access(self, client):
        """测试未授权访问"""
        response = client.get("/api/v1/auth/profile")
        assert response.status_code == 401  # 未授权
    
    def test_invalid_token_format(self, client):
        """测试无效令牌格式"""
        headers = {"Authorization": "Bearer invalid_token"}
        response = client.get("/api/v1/auth/profile", headers=headers)
        assert response.status_code == 401  # 未授权


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
