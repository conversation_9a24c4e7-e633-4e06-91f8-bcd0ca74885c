"""
主应用测试
基于ARCHITECTURE.md规范实现的API测试
"""

import pytest
from fastapi.testclient import TestClient
from app.main import create_app


@pytest.fixture
def client():
    """创建测试客户端"""
    app = create_app()
    return TestClient(app)


def test_health_check(client):
    """测试健康检查接口"""
    response = client.get("/api/v1/health/")
    assert response.status_code == 200

    data = response.json()
    assert data["code"] == 200
    assert data["msg"] == "服务正常"
    assert "data" in data
    assert data["data"]["status"] == "healthy"


def test_detailed_health_check(client):
    """测试详细健康检查接口"""
    response = client.get("/api/v1/health/detailed")
    assert response.status_code == 200

    data = response.json()
    assert data["code"] == 200
    assert "data" in data
    assert "status" in data["data"]
    assert "version" in data["data"]
    assert "services" in data["data"]


def test_metrics(client):
    """测试系统指标接口"""
    response = client.get("/api/v1/health/metrics")
    assert response.status_code == 200

    data = response.json()
    assert data["code"] == 200
    assert "data" in data
    assert "system" in data["data"]
    assert "application" in data["data"]


def test_cors_headers(client):
    """测试CORS头"""
    # 使用GET请求测试CORS头，因为OPTIONS可能不被支持
    response = client.get("/api/v1/health/")
    assert response.status_code == 200

    # 检查CORS头（注意头名称可能是小写）
    headers = {k.lower(): v for k, v in response.headers.items()}
    assert "access-control-allow-origin" in headers or "Access-Control-Allow-Origin" in response.headers


def test_404_error(client):
    """测试404错误处理"""
    response = client.get("/api/v1/nonexistent")
    assert response.status_code == 404


def test_api_prefix(client):
    """测试API前缀"""
    # 测试没有前缀的请求应该返回404
    response = client.get("/health/")
    assert response.status_code == 404

    # 测试有前缀的请求应该正常
    response = client.get("/api/v1/health/")
    assert response.status_code == 200


def test_request_id_header(client):
    """测试请求ID头"""
    response = client.get("/api/v1/health/")
    assert response.status_code == 200

    # 检查是否有请求ID头
    assert "X-Request-ID" in response.headers
    assert len(response.headers["X-Request-ID"]) > 0


def test_process_time_header(client):
    """测试处理时间头"""
    response = client.get("/api/v1/health/")
    assert response.status_code == 200

    # 检查是否有处理时间头
    assert "X-Process-Time" in response.headers
    process_time = float(response.headers["X-Process-Time"])
    assert process_time >= 0


def test_security_headers(client):
    """测试安全头"""
    response = client.get("/api/v1/health/")
    assert response.status_code == 200

    headers = response.headers
    assert headers.get("X-Content-Type-Options") == "nosniff"
    assert headers.get("X-Frame-Options") == "DENY"
    assert headers.get("X-XSS-Protection") == "1; mode=block"
    assert headers.get("Referrer-Policy") == "strict-origin-when-cross-origin"


def test_json_response_format(client):
    """测试JSON响应格式"""
    response = client.get("/api/v1/health/")
    assert response.status_code == 200

    data = response.json()

    # 检查标准响应格式
    assert "code" in data
    assert "msg" in data
    assert "data" in data
    assert "timestamp" in data

    # 检查数据类型
    assert isinstance(data["code"], int)
    assert isinstance(data["msg"], str)
    assert isinstance(data["timestamp"], str)


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
