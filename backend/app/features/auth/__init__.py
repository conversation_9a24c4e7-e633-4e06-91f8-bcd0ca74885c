"""
认证功能模块
基于ARCHITECTURE.md规范实现的用户认证和权限管理模块
"""

from .models import User, Role, Permission, UserConfig, Device, LoginLog, VerificationCode
from .schema import (
    UserLoginRequest, UserRegisterRequest, SMSCodeRequest,
    PasswordResetRequest, PasswordChangeRequest, UserProfileUpdateRequest,
    UserConfigUpdateRequest, TokenRefreshRequest,
    LoginResponse, UserResponse, UserConfigResponse, TokenRefreshResponse
)
from .service import AuthService
from .router import router

__all__ = [
    # 模型
    "User",
    "Role", 
    "Permission",
    "UserConfig",
    "Device",
    "LoginLog",
    "VerificationCode",
    
    # 请求模式
    "UserLoginRequest",
    "UserRegisterRequest",
    "SMSCodeRequest",
    "PasswordResetRequest",
    "PasswordChangeRequest",
    "UserProfileUpdateRequest",
    "UserConfigUpdateRequest",
    "TokenRefreshRequest",
    
    # 响应模式
    "LoginResponse",
    "UserResponse",
    "UserConfigResponse",
    "TokenRefreshResponse",
    
    # 服务
    "AuthService",
    
    # 路由
    "router",
]
