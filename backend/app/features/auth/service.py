"""
认证模块服务层
基于ARCHITECTURE.md规范实现的业务逻辑层
"""
import logging
from datetime import datetime, timedelta
from typing import Op<PERSON>, Tu<PERSON>, Dict, Any
from tortoise.transactions import in_transaction

from app.core.security import Pass<PERSON><PERSON>ana<PERSON>, JWTManager
from app.core.exceptions import AuthenticationError, ValidationError, BusinessError
from app.features.auth.models import User, Device, UserConfig, VerificationCode, LoginLog
from app.features.auth.schema import (
    UserLoginRequest, UserRegisterRequest, PasswordResetRequest,
    UserProfileUpdateRequest, UserConfigUpdateRequest
)

logger = logging.getLogger(__name__)


class AuthService:
    """认证服务"""
    
    def __init__(self, password_manager: PasswordManager, jwt_manager: JWTManager):
        self.password_manager = password_manager
        self.jwt_manager = jwt_manager
    
    async def login(self, login_data: UserLoginRequest, ip_address: str, user_agent: str) -> Dict[str, Any]:
        """用户登录"""
        try:
            # 查找用户
            user = await self._find_user_by_username(login_data.username)
            if not user:
                await self._log_login_attempt(None, login_data, ip_address, user_agent, False, "用户不存在")
                raise AuthenticationError("用户名或密码错误")
            
            # 检查用户状态
            if not user.is_active or user.status != 1:
                await self._log_login_attempt(user, login_data, ip_address, user_agent, False, "用户已禁用")
                raise AuthenticationError("账户已被禁用")
            
            # 验证密码
            if not self.password_manager.verify_password(login_data.password, user.password):
                await self._log_login_attempt(user, login_data, ip_address, user_agent, False, "密码错误")
                raise AuthenticationError("用户名或密码错误")
            
            # 更新用户登录信息
            user.last_login = datetime.now()
            user.login_count += 1
            await user.save()
            
            # 处理设备信息
            device = await self._handle_device(user, login_data, ip_address, user_agent)
            
            # 生成令牌
            access_token = self.jwt_manager.create_access_token({"sub": user.id})
            refresh_token = self.jwt_manager.create_refresh_token({"sub": user.id})
            
            # 记录登录日志
            await self._log_login_attempt(user, login_data, ip_address, user_agent, True, None, device)
            
            logger.info(f"用户登录成功: {user.id}", extra={
                "user_id": user.id,
                "ip_address": ip_address,
                "device_id": login_data.device_id
            })

            return {
                "access_token": access_token,
                "refresh_token": refresh_token,
                "token_type": "bearer",
                "expires_in": self.jwt_manager.access_token_expire_minutes * 60,
                "user": await self._serialize_user(user)
            }
            
        except AuthenticationError:
            raise
        except Exception as e:
            logger.error(f"登录过程中发生错误: {str(e)}", exc_info=True)
            raise BusinessError("登录失败，请稍后重试")
    
    async def register(self, register_data: UserRegisterRequest, ip_address: str, user_agent: str) -> Dict[str, Any]:
        """用户注册"""
        try:
            # 验证验证码
            if not await self._verify_sms_code(register_data.mobile, register_data.verification_code, "register"):
                raise ValidationError("验证码无效或已过期")
            
            # 检查用户是否已存在
            existing_user = await User.filter(mobile=register_data.mobile).first()
            if existing_user:
                raise ValidationError("手机号已被注册")
            
            if register_data.username:
                existing_username = await User.filter(username=register_data.username).first()
                if existing_username:
                    raise ValidationError("用户名已被使用")
            
            async with in_transaction():
                # 创建用户
                user = await User.create(
                    username=register_data.username,
                    mobile=register_data.mobile,
                    password=self.password_manager.hash_password(register_data.password),
                    nickname=register_data.nickname or register_data.username,
                    is_active=True,
                    status=1,
                    login_count=0
                )
                
                # 创建用户配置
                await UserConfig.create(user=user)
                
                # 标记验证码为已使用
                await VerificationCode.filter(
                    mobile=register_data.mobile,
                    code=register_data.verification_code,
                    code_type="register",
                    is_used=False
                ).update(is_used=True)
            
            logger.info(f"用户注册成功: {user.id}", extra={
                "user_id": user.id,
                "mobile": register_data.mobile,
                "ip_address": ip_address
            })
            
            return await self._serialize_user(user)
            
        except (ValidationError, BusinessError):
            raise
        except Exception as e:
            logger.error(f"注册过程中发生错误: {str(e)}", exc_info=True)
            raise BusinessError("注册失败，请稍后重试")
    
    async def reset_password(self, reset_data: PasswordResetRequest) -> bool:
        """重置密码"""
        try:
            # 验证验证码
            if not await self._verify_sms_code(reset_data.mobile, reset_data.verification_code, "reset"):
                raise ValidationError("验证码无效或已过期")
            
            # 查找用户
            user = await User.filter(mobile=reset_data.mobile).first()
            if not user:
                raise ValidationError("用户不存在")
            
            async with in_transaction():
                # 更新密码
                user.password = self.password_manager.hash_password(reset_data.new_password)
                await user.save()
                
                # 标记验证码为已使用
                await VerificationCode.filter(
                    mobile=reset_data.mobile,
                    code=reset_data.verification_code,
                    code_type="reset",
                    is_used=False
                ).update(is_used=True)
            
            logger.info(f"用户密码重置成功: {user.id}", extra={"user_id": user.id})
            return True
            
        except (ValidationError, BusinessError):
            raise
        except Exception as e:
            logger.error(f"密码重置过程中发生错误: {str(e)}", exc_info=True)
            raise BusinessError("密码重置失败，请稍后重试")
    
    async def update_profile(self, user_id: str, update_data: UserProfileUpdateRequest) -> Dict[str, Any]:
        """更新用户资料"""
        try:
            user = await User.get(id=user_id)
            
            # 更新字段
            update_fields = {}
            if update_data.nickname is not None:
                update_fields["nickname"] = update_data.nickname
            if update_data.avatar is not None:
                update_fields["avatar"] = update_data.avatar
            if update_data.intro is not None:
                update_fields["intro"] = update_data.intro
            if update_data.email is not None:
                # 检查邮箱是否已被使用
                existing_email = await User.filter(email=update_data.email).exclude(id=user_id).first()
                if existing_email:
                    raise ValidationError("邮箱已被使用")
                update_fields["email"] = update_data.email
            
            if update_fields:
                await User.filter(id=user_id).update(**update_fields)
                user = await User.get(id=user_id)
            
            logger.info(f"用户资料更新成功: {user_id}", extra={"user_id": user_id})
            return await self._serialize_user(user)
            
        except ValidationError:
            raise
        except Exception as e:
            logger.error(f"更新用户资料时发生错误: {str(e)}", exc_info=True)
            raise BusinessError("更新失败，请稍后重试")
    
    async def get_user_by_id(self, user_id: str) -> Optional[Dict[str, Any]]:
        """根据ID获取用户"""
        try:
            user = await User.get_or_none(id=user_id)
            if user:
                return await self._serialize_user(user)
            return None
        except Exception as e:
            logger.error(f"获取用户信息时发生错误: {str(e)}", exc_info=True)
            return None
    
    async def refresh_token(self, refresh_token: str) -> Dict[str, Any]:
        """刷新令牌"""
        try:
            payload = self.jwt_manager.verify_token(refresh_token)
            if payload.get("type") != "refresh":
                raise AuthenticationError("无效的刷新令牌")
            
            user_id = payload.get("sub")
            user = await User.get_or_none(id=user_id)
            if not user or not user.is_active:
                raise AuthenticationError("用户不存在或已禁用")
            
            # 生成新的访问令牌
            access_token = self.jwt_manager.create_access_token({"sub": user_id})
            
            return {
                "access_token": access_token,
                "token_type": "bearer",
                "expires_in": self.jwt_manager.access_token_expire_minutes * 60
            }
            
        except AuthenticationError:
            raise
        except Exception as e:
            logger.error(f"刷新令牌时发生错误: {str(e)}", exc_info=True)
            raise AuthenticationError("令牌刷新失败")
    
    async def _find_user_by_username(self, username: str) -> Optional[User]:
        """根据用户名查找用户"""
        # 尝试用户名、手机号、邮箱查找
        user = await User.filter(username=username).first()
        if not user:
            user = await User.filter(mobile=username).first()
        if not user:
            user = await User.filter(email=username).first()
        return user
    
    async def _handle_device(self, user: User, login_data: UserLoginRequest, ip_address: str, user_agent: str) -> Device:
        """处理设备信息"""
        device = None
        if login_data.device_id:
            device = await Device.filter(user=user, device_id=login_data.device_id).first()
        
        if device:
            # 更新现有设备
            device.device_name = login_data.device_name or device.device_name
            device.device_type = login_data.device_type or device.device_type
            device.platform = login_data.platform or device.platform
            device.app_version = login_data.app_version or device.app_version
            device.ip_address = ip_address
            device.user_agent = user_agent
            device.last_active = datetime.now()
            device.login_count += 1
            device.is_active = True
            await device.save()
        else:
            # 创建新设备
            device = await Device.create(
                user=user,
                device_id=login_data.device_id or f"unknown_{datetime.now().timestamp()}",
                device_name=login_data.device_name or "Unknown Device",
                device_type=login_data.device_type or "unknown",
                platform=login_data.platform or "unknown",
                app_version=login_data.app_version or "1.0.0",
                ip_address=ip_address,
                user_agent=user_agent,
                is_active=True
            )
        
        return device
    
    async def _log_login_attempt(
        self, user: Optional[User], login_data: UserLoginRequest, 
        ip_address: str, user_agent: str, is_success: bool, 
        failure_reason: Optional[str] = None, device: Optional[Device] = None
    ):
        """记录登录尝试"""
        try:
            await LoginLog.create(
                user=user,
                device=device,
                login_type="password",
                ip_address=ip_address,
                user_agent=user_agent,
                is_success=is_success,
                failure_reason=failure_reason
            )
        except Exception as e:
            logger.error(f"记录登录日志失败: {str(e)}")
    
    async def _verify_sms_code(self, mobile: str, code: str, code_type: str) -> bool:
        """验证短信验证码"""
        verification = await VerificationCode.filter(
            mobile=mobile,
            code=code,
            code_type=code_type,
            is_used=False
        ).first()
        
        if not verification:
            return False
        
        return verification.is_valid
    
    async def _serialize_user(self, user: User) -> Dict[str, Any]:
        """序列化用户信息"""
        return {
            "id": user.id,
            "username": user.username,
            "mobile": user.mobile,
            "email": user.email,
            "nickname": user.nickname,
            "avatar": user.avatar,
            "intro": user.intro,
            "is_active": user.is_active,
            "last_login": user.last_login,
            "login_count": user.login_count,
            "created_at": user.created_at,
            "updated_at": user.updated_at
        }
