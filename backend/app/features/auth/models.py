"""
认证模块数据模型
基于ARCHITECTURE.md规范实现的用户认证相关数据模型
"""
import shortuuid
from tortoise import fields
from tortoise.models import Model
from datetime import datetime
from typing import Optional


class TimeStampModelMixin(Model):
    """时间戳混入模型"""
    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")
    updated_at = fields.DatetimeField(auto_now=True, description="更新时间")
    
    class Meta:
        abstract = True


class User(TimeStampModelMixin):
    """用户模型"""
    
    # 关系字段
    role: fields.ManyToManyRelation["Role"] = fields.ManyToManyField(
        "models.Role", related_name="users", on_delete=fields.CASCADE
    )
    
    # 基本字段
    id = fields.CharField(
        max_length=100,
        pk=True,
        description="用户唯一标识",
        unique=True,
        default=shortuuid.uuid,
    )
    username = fields.Char<PERSON>ield(
        max_length=30,
        null=True,
        unique=True,
        description="用户名",
    )
    password = fields.CharField(max_length=100, description="用户密码")
    mobile = fields.CharField(max_length=20, null=True, unique=True, description="手机号码")
    email = fields.CharField(max_length=100, null=True, unique=True, description="用户邮箱")
    
    # 个人信息
    avatar = fields.CharField(max_length=200, null=True, description="用户头像URL")
    intro = fields.TextField(null=True, description="个人简介")
    nickname = fields.CharField(max_length=50, null=True, description="昵称")
    
    # 状态字段
    is_superuser = fields.BooleanField(default=False, description="是否为超级管理员")
    is_active = fields.BooleanField(default=True, description="是否激活")
    status = fields.IntField(default=1, description="用户状态: 1正常, 2禁用, 3已删除")
    
    # 登录相关
    last_login = fields.DatetimeField(null=True, description="最后登录时间")
    login_count = fields.IntField(default=0, description="登录次数")
    
    class PydanticMeta:
        exclude = ["password"]
    
    class Meta:
        table = "user"
        table_description = "用户表"
        ordering = ["-created_at", "id"]
    
    def __str__(self):
        return self.username or self.mobile or self.id


class Role(TimeStampModelMixin):
    """角色模型"""
    
    id = fields.IntField(pk=True)
    name = fields.CharField(max_length=50, unique=True, description="角色名称")
    code = fields.CharField(max_length=50, unique=True, description="角色代码")
    description = fields.CharField(max_length=255, null=True, description="角色描述")
    is_active = fields.BooleanField(default=True, description="是否启用")
    
    # 权限关系
    permissions: fields.ManyToManyRelation["Permission"] = fields.ManyToManyField(
        "models.Permission", related_name="roles"
    )
    
    class Meta:
        table = "role"
        table_description = "角色表"
        ordering = ["id"]
    
    def __str__(self):
        return self.name


class Permission(TimeStampModelMixin):
    """权限模型"""
    
    id = fields.IntField(pk=True)
    name = fields.CharField(max_length=50, description="权限名称")
    code = fields.CharField(max_length=100, unique=True, description="权限代码")
    description = fields.CharField(max_length=255, null=True, description="权限描述")
    resource = fields.CharField(max_length=100, description="资源标识")
    action = fields.CharField(max_length=50, description="操作类型")
    is_active = fields.BooleanField(default=True, description="是否启用")
    
    class Meta:
        table = "permission"
        table_description = "权限表"
        ordering = ["id"]
    
    def __str__(self):
        return f"{self.resource}:{self.action}"


class UserConfig(TimeStampModelMixin):
    """用户配置模型"""
    
    id = fields.IntField(pk=True)
    user: fields.OneToOneRelation[User] = fields.OneToOneField(
        "models.User", related_name="config", on_delete=fields.CASCADE
    )
    
    # 学习配置
    is_auto_play_audio = fields.BooleanField(default=True, description="是否自动播放音频")
    is_auto_play_ai_audio = fields.BooleanField(default=True, description="是否自动播放AI音频")
    review_number = fields.IntField(default=30, description="复习数量")
    study_number = fields.IntField(default=10, description="学习数量")
    
    # 当前状态
    editing_book_id = fields.CharField(max_length=100, null=True, description="当前编辑的书籍ID")
    current_study_book_id = fields.CharField(max_length=100, null=True, description="当前学习的书籍ID")
    
    class Meta:
        table = "user_config"
        table_description = "用户配置表"


class Device(TimeStampModelMixin):
    """用户设备模型"""
    
    id = fields.CharField(
        max_length=100,
        pk=True,
        description="设备唯一标识",
        default=shortuuid.uuid,
    )
    user: fields.ForeignKeyRelation[User] = fields.ForeignKeyField(
        "models.User", related_name="devices", on_delete=fields.CASCADE
    )
    
    # 设备信息
    device_id = fields.CharField(max_length=100, description="设备ID")
    device_name = fields.CharField(max_length=100, description="设备名称")
    device_type = fields.CharField(max_length=50, description="设备类型")
    platform = fields.CharField(max_length=50, description="平台")
    app_version = fields.CharField(max_length=20, description="应用版本")
    
    # 网络信息
    ip_address = fields.CharField(max_length=45, null=True, description="IP地址")
    user_agent = fields.TextField(null=True, description="用户代理")
    
    # 状态信息
    last_active = fields.DatetimeField(auto_now=True, description="最后活跃时间")
    is_active = fields.BooleanField(default=True, description="是否活跃")
    login_count = fields.IntField(default=1, description="登录次数")
    
    class PydanticMeta:
        exclude = ["user_agent", "ip_address"]
    
    class Meta:
        table = "device"
        table_description = "用户设备表"
        ordering = ["-last_active", "id"]
    
    def __str__(self):
        return f"{self.device_name} ({self.device_type})"


class LoginLog(TimeStampModelMixin):
    """登录日志模型"""
    
    id = fields.IntField(pk=True)
    user: fields.ForeignKeyRelation[User] = fields.ForeignKeyField(
        "models.User", related_name="login_logs", on_delete=fields.CASCADE
    )
    device: fields.ForeignKeyRelation[Device] = fields.ForeignKeyField(
        "models.Device", related_name="login_logs", on_delete=fields.CASCADE, null=True
    )
    
    # 登录信息
    login_type = fields.CharField(max_length=20, description="登录类型: password, sms, oauth")
    ip_address = fields.CharField(max_length=45, description="登录IP")
    user_agent = fields.TextField(null=True, description="用户代理")
    location = fields.CharField(max_length=100, null=True, description="登录地点")
    
    # 状态信息
    is_success = fields.BooleanField(description="是否成功")
    failure_reason = fields.CharField(max_length=100, null=True, description="失败原因")
    
    class Meta:
        table = "login_log"
        table_description = "登录日志表"
        ordering = ["-created_at"]


class VerificationCode(TimeStampModelMixin):
    """验证码模型"""
    
    id = fields.IntField(pk=True)
    
    # 验证码信息
    mobile = fields.CharField(max_length=20, description="手机号")
    code = fields.CharField(max_length=10, description="验证码")
    code_type = fields.CharField(max_length=20, description="验证码类型: login, register, reset")
    
    # 状态信息
    is_used = fields.BooleanField(default=False, description="是否已使用")
    expires_at = fields.DatetimeField(description="过期时间")
    
    class Meta:
        table = "verification_code"
        table_description = "验证码表"
        ordering = ["-created_at"]
    
    @property
    def is_expired(self) -> bool:
        """是否已过期"""
        return datetime.now() > self.expires_at
    
    @property
    def is_valid(self) -> bool:
        """是否有效"""
        return not self.is_used and not self.is_expired
