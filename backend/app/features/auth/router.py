"""
认证模块路由
基于ARCHITECTURE.md规范实现的API路由定义
"""

from fastapi import APIRouter, Depends, Request, HTTPException, status
from typing import Dict, Any

from app.core.dependencies import get_jwt_manager, get_password_manager, get_current_user
from app.core.responses import ApiResponse, resmod
from app.core.security import <PERSON><PERSON>TManager, PasswordManager
from app.features.auth.service import AuthService
from app.features.auth.schema import (
    UserLoginRequest,
    UserRegisterRequest,
    SMSCodeRequest,
    PasswordResetRequest,
    PasswordChangeRequest,
    UserProfileUpdateRequest,
    UserConfigUpdateRequest,
    TokenRefreshRequest,
    LoginResponse,
    UserResponse,
    UserConfigResponse,
    TokenRefreshResponse,
)
from app.features.auth.models import User, UserConfig

# 创建路由器
router = APIRouter()


async def get_auth_service(
    password_manager: PasswordManager = Depends(get_password_manager),
    jwt_manager: JWTManager = Depends(get_jwt_manager),
) -> AuthService:
    """获取认证服务实例"""
    return AuthService(password_manager, jwt_manager)


@router.post(
    "/login",
    summary="用户登录",
    description="使用用户名/手机号/邮箱和密码进行登录",
    response_model=resmod(LoginResponse),
)
async def login(
    login_data: UserLoginRequest, request: Request, auth_service: AuthService = Depends(get_auth_service)
):
    """用户登录接口"""
    ip_address = request.client.host if request.client else "unknown"
    user_agent = request.headers.get("user-agent", "unknown")

    result = await auth_service.login(login_data, ip_address, user_agent)
    return ApiResponse.success(data=result, msg="登录成功")


@router.post(
    "/register",
    summary="用户注册",
    description="使用手机号和验证码进行用户注册",
    response_model=resmod(UserResponse),
)
async def register(
    register_data: UserRegisterRequest,
    request: Request,
    auth_service: AuthService = Depends(get_auth_service),
):
    """用户注册接口"""
    ip_address = request.client.host if request.client else "unknown"
    user_agent = request.headers.get("user-agent", "unknown")

    result = await auth_service.register(register_data, ip_address, user_agent)
    return ApiResponse.created(data=result, msg="注册成功")


@router.post("/sms-code", summary="发送短信验证码", description="发送短信验证码到指定手机号")
async def send_sms_code(sms_data: SMSCodeRequest):
    """发送短信验证码接口"""
    # 这里需要集成短信服务
    # 暂时返回成功响应
    return ApiResponse.success(msg="验证码发送成功")


@router.post("/reset-password", summary="重置密码", description="使用手机号和验证码重置密码")
async def reset_password(
    reset_data: PasswordResetRequest, auth_service: AuthService = Depends(get_auth_service)
):
    """重置密码接口"""
    await auth_service.reset_password(reset_data)
    return ApiResponse.success(msg="密码重置成功")


@router.post("/change-password", summary="修改密码", description="修改当前用户密码")
async def change_password(
    change_data: PasswordChangeRequest,
    current_user: User = Depends(get_current_user),
    password_manager: PasswordManager = Depends(get_password_manager),
):
    """修改密码接口"""
    # 验证原密码
    if not password_manager.verify_password(change_data.old_password, current_user.password):
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="原密码不正确")

    # 更新密码
    current_user.password = password_manager.hash_password(change_data.new_password)
    await current_user.save()

    return ApiResponse.success(msg="密码修改成功")


@router.post(
    "/refresh-token",
    summary="刷新令牌",
    description="使用刷新令牌获取新的访问令牌",
    response_model=resmod(TokenRefreshResponse),
)
async def refresh_token(
    refresh_data: TokenRefreshRequest, auth_service: AuthService = Depends(get_auth_service)
):
    """刷新令牌接口"""
    result = await auth_service.refresh_token(refresh_data.refresh_token)
    return ApiResponse.success(data=result, msg="令牌刷新成功")


@router.get(
    "/profile",
    summary="获取用户资料",
    description="获取当前用户的详细资料",
    response_model=resmod(UserResponse),
)
async def get_profile(
    current_user: User = Depends(get_current_user), auth_service: AuthService = Depends(get_auth_service)
):
    """获取用户资料接口"""
    result = await auth_service.get_user_by_id(current_user.id)
    return ApiResponse.success(data=result, msg="获取成功")


@router.put(
    "/profile",
    summary="更新用户资料",
    description="更新当前用户的资料信息",
    response_model=resmod(UserResponse),
)
async def update_profile(
    update_data: UserProfileUpdateRequest,
    current_user: User = Depends(get_current_user),
    auth_service: AuthService = Depends(get_auth_service),
):
    """更新用户资料接口"""
    result = await auth_service.update_profile(current_user.id, update_data)
    return ApiResponse.updated(data=result, msg="更新成功")


@router.get(
    "/config",
    summary="获取用户配置",
    description="获取当前用户的配置信息",
    response_model=resmod(UserConfigResponse),
)
async def get_user_config(current_user: User = Depends(get_current_user)):
    """获取用户配置接口"""
    config = await UserConfig.get_or_none(user=current_user)
    if not config:
        # 创建默认配置
        config = await UserConfig.create(user=current_user)

    result = {
        "is_auto_play_audio": config.is_auto_play_audio,
        "is_auto_play_ai_audio": config.is_auto_play_ai_audio,
        "review_number": config.review_number,
        "study_number": config.study_number,
        "editing_book_id": config.editing_book_id,
        "current_study_book_id": config.current_study_book_id,
    }

    return ApiResponse.success(data=result, msg="获取成功")


@router.put(
    "/config",
    summary="更新用户配置",
    description="更新当前用户的配置信息",
    response_model=resmod(UserConfigResponse),
)
async def update_user_config(
    update_data: UserConfigUpdateRequest, current_user: User = Depends(get_current_user)
):
    """更新用户配置接口"""
    config = await UserConfig.get_or_none(user=current_user)
    if not config:
        config = await UserConfig.create(user=current_user)

    # 更新配置
    update_fields = {}
    if update_data.is_auto_play_audio is not None:
        update_fields["is_auto_play_audio"] = update_data.is_auto_play_audio
    if update_data.is_auto_play_ai_audio is not None:
        update_fields["is_auto_play_ai_audio"] = update_data.is_auto_play_ai_audio
    if update_data.review_number is not None:
        update_fields["review_number"] = update_data.review_number
    if update_data.study_number is not None:
        update_fields["study_number"] = update_data.study_number

    if update_fields:
        await UserConfig.filter(user=current_user).update(**update_fields)
        config = await UserConfig.get(user=current_user)

    result = {
        "is_auto_play_audio": config.is_auto_play_audio,
        "is_auto_play_ai_audio": config.is_auto_play_ai_audio,
        "review_number": config.review_number,
        "study_number": config.study_number,
        "editing_book_id": config.editing_book_id,
        "current_study_book_id": config.current_study_book_id,
    }

    return ApiResponse.updated(data=result, msg="更新成功")


@router.post("/logout", summary="用户登出", description="用户登出（客户端需要清除本地令牌）")
async def logout(current_user: User = Depends(get_current_user)):
    """用户登出接口"""
    # 这里可以实现令牌黑名单机制
    # 目前只是返回成功响应，实际的登出由客户端处理
    return ApiResponse.success(msg="登出成功")


@router.get("/devices", summary="获取用户设备列表", description="获取当前用户的所有设备信息")
async def get_user_devices(current_user: User = Depends(get_current_user)):
    """获取用户设备列表接口"""
    devices = await current_user.devices.all()

    result = []
    for device in devices:
        result.append(
            {
                "id": device.id,
                "device_id": device.device_id,
                "device_name": device.device_name,
                "device_type": device.device_type,
                "platform": device.platform,
                "app_version": device.app_version,
                "last_active": device.last_active,
                "is_active": device.is_active,
                "login_count": device.login_count,
            }
        )

    return ApiResponse.success(data=result, msg="获取成功")
