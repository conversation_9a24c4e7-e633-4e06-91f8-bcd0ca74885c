"""
响应处理模块
基于ARCHITECTURE.md规范实现的标准化响应格式
"""
from typing import Any, Optional, Dict, List, Union
from pydantic import BaseModel
from fastapi.responses import JSONResponse
from datetime import datetime


class HttpCode:
    """HTTP状态码常量"""
    SUCCESS = 200
    CREATED = 201
    NO_CONTENT = 204
    BAD_REQUEST = 400
    UNAUTHORIZED = 401
    FORBIDDEN = 403
    NOT_FOUND = 404
    METHOD_NOT_ALLOWED = 405
    CONFLICT = 409
    UNPROCESSABLE_ENTITY = 422
    INTERNAL_SERVER_ERROR = 500
    BAD_GATEWAY = 502
    SERVICE_UNAVAILABLE = 503


class BaseResponseModel(BaseModel):
    """基础响应模型"""
    code: int = HttpCode.SUCCESS
    msg: str = "success"
    data: Any = []
    timestamp: Optional[datetime] = None
    request_id: Optional[str] = None

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat() if v else None
        }


class PaginationMeta(BaseModel):
    """分页元数据"""
    page: int
    size: int
    total: int
    pages: int
    has_next: bool
    has_prev: bool


class PaginatedResponseModel(BaseResponseModel):
    """分页响应模型"""
    data: List[Any] = []
    meta: Optional[PaginationMeta] = None


class ApiResponse:
    """API响应工具类"""
    
    @staticmethod
    def success(
        data: Any = None,
        msg: str = "操作成功",
        code: int = HttpCode.SUCCESS,
        request_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """成功响应"""
        return {
            "code": code,
            "msg": msg,
            "data": data if data is not None else [],
            "timestamp": datetime.now(),
            "request_id": request_id
        }
    
    @staticmethod
    def fail(
        msg: str = "操作失败",
        code: int = HttpCode.BAD_REQUEST,
        data: Any = None,
        request_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """失败响应"""
        return {
            "code": code,
            "msg": msg,
            "data": data if data is not None else [],
            "timestamp": datetime.now(),
            "request_id": request_id
        }
    
    @staticmethod
    def created(
        data: Any = None,
        msg: str = "创建成功",
        request_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """创建成功响应"""
        return ApiResponse.success(
            data=data,
            msg=msg,
            code=HttpCode.CREATED,
            request_id=request_id
        )
    
    @staticmethod
    def updated(
        data: Any = None,
        msg: str = "更新成功",
        request_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """更新成功响应"""
        return ApiResponse.success(
            data=data,
            msg=msg,
            request_id=request_id
        )
    
    @staticmethod
    def deleted(
        msg: str = "删除成功",
        request_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """删除成功响应"""
        return ApiResponse.success(
            msg=msg,
            request_id=request_id
        )
    
    @staticmethod
    def not_found(
        msg: str = "资源不存在",
        request_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """资源不存在响应"""
        return ApiResponse.fail(
            msg=msg,
            code=HttpCode.NOT_FOUND,
            request_id=request_id
        )
    
    @staticmethod
    def unauthorized(
        msg: str = "未授权访问",
        request_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """未授权响应"""
        return ApiResponse.fail(
            msg=msg,
            code=HttpCode.UNAUTHORIZED,
            request_id=request_id
        )
    
    @staticmethod
    def forbidden(
        msg: str = "权限不足",
        request_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """权限不足响应"""
        return ApiResponse.fail(
            msg=msg,
            code=HttpCode.FORBIDDEN,
            request_id=request_id
        )
    
    @staticmethod
    def validation_error(
        msg: str = "数据验证失败",
        data: Any = None,
        request_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """数据验证失败响应"""
        return ApiResponse.fail(
            msg=msg,
            code=HttpCode.UNPROCESSABLE_ENTITY,
            data=data,
            request_id=request_id
        )
    
    @staticmethod
    def paginated(
        data: List[Any],
        page: int,
        size: int,
        total: int,
        msg: str = "查询成功",
        request_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """分页响应"""
        pages = (total + size - 1) // size  # 向上取整
        
        meta = PaginationMeta(
            page=page,
            size=size,
            total=total,
            pages=pages,
            has_next=page < pages,
            has_prev=page > 1
        )
        
        return {
            "code": HttpCode.SUCCESS,
            "msg": msg,
            "data": data,
            "meta": meta.dict(),
            "timestamp": datetime.now(),
            "request_id": request_id
        }


class ResponseBuilder:
    """响应构建器"""
    
    def __init__(self):
        self.response_data = {
            "code": HttpCode.SUCCESS,
            "msg": "success",
            "data": [],
            "timestamp": datetime.now()
        }
    
    def code(self, code: int) -> 'ResponseBuilder':
        """设置响应码"""
        self.response_data["code"] = code
        return self
    
    def message(self, msg: str) -> 'ResponseBuilder':
        """设置响应消息"""
        self.response_data["msg"] = msg
        return self
    
    def data(self, data: Any) -> 'ResponseBuilder':
        """设置响应数据"""
        self.response_data["data"] = data
        return self
    
    def request_id(self, request_id: str) -> 'ResponseBuilder':
        """设置请求ID"""
        self.response_data["request_id"] = request_id
        return self
    
    def build(self) -> Dict[str, Any]:
        """构建响应"""
        return self.response_data
    
    def json_response(self, status_code: int = 200) -> JSONResponse:
        """构建JSON响应"""
        return JSONResponse(
            content=self.response_data,
            status_code=status_code
        )


def resmod(data_model: BaseModel) -> type:
    """
    创建响应模型装饰器
    用于FastAPI的response_model参数
    """
    class ResponseModel(BaseResponseModel):
        data: data_model
    
    return ResponseModel


def paginated_resmod(data_model: BaseModel) -> type:
    """
    创建分页响应模型装饰器
    用于FastAPI的response_model参数
    """
    class PaginatedResponseModel(BaseResponseModel):
        data: List[data_model]
        meta: PaginationMeta
    
    return PaginatedResponseModel


# 常用响应实例
def success_response(data: Any = None, msg: str = "操作成功") -> JSONResponse:
    """成功响应的JSONResponse"""
    return JSONResponse(
        content=ApiResponse.success(data=data, msg=msg),
        status_code=200
    )


def error_response(msg: str = "操作失败", code: int = HttpCode.BAD_REQUEST) -> JSONResponse:
    """错误响应的JSONResponse"""
    return JSONResponse(
        content=ApiResponse.fail(msg=msg, code=code),
        status_code=code if 400 <= code < 600 else 400
    )
