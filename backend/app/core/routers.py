"""
路由管理模块
基于ARCHITECTURE.md规范实现的路由聚合系统
"""

from fastapi import APIRouter
from typing import Type
from tortoise import Model

from app.core.settings import get_settings

settings = get_settings()


# 主从数据库的路由配置
class DatabaseRouter:
    """数据库路由配置"""

    def db_for_read(self, model: Type[Model]):
        """读操作数据库选择"""
        return "default"  # 可以配置为从库

    def db_for_write(self, model: Type[Model]):
        """写操作数据库选择"""
        return "default"  # 主库


# 创建主路由
AppRoutes = APIRouter(prefix=settings.API_V1_PREFIX)


def register_routes():
    """注册所有路由"""

    # 认证相关路由
    try:
        from app.features.auth.router import router as auth_router

        AppRoutes.include_router(auth_router, prefix="/auth", tags=["认证管理"])
    except ImportError as e:
        print(f"Warning: Could not import auth router: {e}")
        pass

    # 学习系统路由
    try:
        from app.features.study.router import router as study_router

        AppRoutes.include_router(study_router, tags=["学习系统"])
    except ImportError:
        pass

    # 通用功能路由
    try:
        from app.features.general.router import router as general_router

        AppRoutes.include_router(general_router, tags=["通用功能"])
    except ImportError:
        pass

    # 发音功能路由
    try:
        from app.features.pronunciation.router import router as pronunciation_router

        AppRoutes.include_router(pronunciation_router, tags=["发音功能"])
    except ImportError:
        pass

    # 腾讯云服务路由
    try:
        from app.features.tcyun.router import router as tcyun_router

        AppRoutes.include_router(tcyun_router, tags=["腾讯云服务"])
    except ImportError:
        pass

    # 健康检查路由
    AppRoutes.include_router(create_health_router(), tags=["系统监控"])


def create_health_router() -> APIRouter:
    """创建健康检查路由"""
    router = APIRouter(prefix="/health")

    @router.get("/")
    async def health_check():
        """基础健康检查"""
        from app.core.responses import ApiResponse

        return ApiResponse.success(data={"status": "healthy"}, msg="服务正常")

    @router.get("/detailed")
    async def detailed_health_check():
        """详细健康检查"""
        from app.core.responses import ApiResponse
        from app.core.database import DatabaseManager

        health_status = {
            "status": "healthy",
            "version": settings.APP_VERSION,
            "timestamp": None,
            "services": {},
        }

        # 检查数据库连接
        try:
            db_healthy = await DatabaseManager.health_check()
            health_status["services"]["database"] = {
                "status": "healthy" if db_healthy else "unhealthy",
                "details": await DatabaseManager.get_connection_info() if db_healthy else None,
            }
        except Exception as e:
            health_status["services"]["database"] = {"status": "unhealthy", "error": str(e)}

        # 检查Redis连接
        try:
            # 这里可以添加Redis健康检查
            health_status["services"]["redis"] = {"status": "healthy"}
        except Exception as e:
            health_status["services"]["redis"] = {"status": "unhealthy", "error": str(e)}

        # 判断整体健康状态
        all_healthy = all(
            service.get("status") == "healthy" for service in health_status["services"].values()
        )

        if not all_healthy:
            health_status["status"] = "unhealthy"

        return ApiResponse.success(data=health_status, msg="健康检查完成")

    @router.get("/metrics")
    async def metrics():
        """系统指标"""
        from app.core.responses import ApiResponse
        import psutil
        import time

        metrics_data = {
            "system": {
                "cpu_percent": psutil.cpu_percent(interval=1),
                "memory_percent": psutil.virtual_memory().percent,
                "disk_percent": psutil.disk_usage("/").percent,
                "uptime": time.time() - psutil.boot_time(),
            },
            "application": {"version": settings.APP_VERSION, "debug_mode": settings.DEBUG},
        }

        return ApiResponse.success(data=metrics_data, msg="系统指标获取成功")

    return router


# 注册所有路由
register_routes()
