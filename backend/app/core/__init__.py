"""
核心模块初始化
基于ARCHITECTURE.md规范的核心功能模块
"""

# 导出核心组件
from .settings import get_settings, Settings
from .database import DatabaseManager, TORTOISE_ORM
from .dependencies import (
    get_current_user,
    get_current_user_id,
    get_admin_user,
    get_jwt_manager,
    get_password_manager,
    validate_pagination
)
from .security import <PERSON><PERSON>TManager, PasswordManager, PermissionManager, SecurityUtils
from .responses import ApiResponse, ResponseBuilder, resmod, paginated_resmod
from .exceptions import (
    CheeStackException,
    AuthenticationError,
    AuthorizationError,
    ValidationError,
    NotFoundError,
    BusinessError
)

__all__ = [
    # 设置
    "get_settings",
    "Settings",
    
    # 数据库
    "DatabaseManager",
    "TORTOISE_ORM",
    
    # 依赖注入
    "get_current_user",
    "get_current_user_id", 
    "get_admin_user",
    "get_jwt_manager",
    "get_password_manager",
    "validate_pagination",
    
    # 安全
    "JWTManager",
    "PasswordManager",
    "PermissionManager",
    "SecurityUtils",
    
    # 响应
    "ApiResponse",
    "ResponseBuilder",
    "resmod",
    "paginated_resmod",
    
    # 异常
    "CheeStackException",
    "AuthenticationError",
    "AuthorizationError",
    "ValidationError",
    "NotFoundError",
    "BusinessError",
]
