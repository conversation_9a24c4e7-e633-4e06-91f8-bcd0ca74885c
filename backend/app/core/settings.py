"""
应用配置管理
基于ARCHITECTURE.md规范实现的分层配置系统
"""
import os
from pathlib import Path
from pydantic_settings import BaseSettings
from fastapi.templating import Jinja2Templates


class Settings(BaseSettings):
    """应用配置类"""
    
    # 应用基本信息
    APP_VERSION: str = "2.0.0"
    APP_NAME: str = "CheeStack API"
    DEBUG: bool = False
    
    # 工作目录
    WORKDIR: str = Path(__file__).parent.parent.parent.as_posix()
    
    # 数据库配置
    DATABASE_HOST: str = "127.0.0.1"
    DATABASE_PORT: int = 5432
    DATABASE_USER: str = "lyuscott"
    DATABASE_PASSWORD: str = "test1234"
    DATABASE_NAME: str = "cheestack"
    DATABASE_URL: str = f"postgres://{DATABASE_USER}:{DATABASE_PASSWORD}@{DATABASE_HOST}:{DATABASE_PORT}/{DATABASE_NAME}"
    
    # Redis配置
    REDIS_HOST: str = "127.0.0.1"
    REDIS_PORT: int = 6379
    REDIS_PASSWORD: str = ""
    REDIS_DB: int = 0
    REDIS_URL: str = f"redis://:{REDIS_PASSWORD}@{REDIS_HOST}:{REDIS_PORT}/{REDIS_DB}"
    
    # JWT配置
    JWT_SECRET_KEY: str = "09d25e094baa6ca2556c818166b7a9563b83f7099f6f0f4caa6cf63b88e8d3e7"
    JWT_ALGORITHM: str = "HS256"
    JWT_ACCESS_TOKEN_EXPIRE_MINUTES: int = 365 * 24 * 60
    
    # 安全配置
    SECRET_KEY: str = "your-secret-key-here"
    CORS_ORIGINS: list[str] = ["*"]
    CORS_ALLOW_CREDENTIALS: bool = True
    CORS_ALLOW_METHODS: list[str] = ["*"]
    CORS_ALLOW_HEADERS: list[str] = ["*"]
    
    # 文件存储配置
    STATIC_DIR: str = os.path.join(WORKDIR, "static")
    TEMPLATE_DIR: str = os.path.join(STATIC_DIR, "templates")
    UPLOAD_DIR: str = os.path.join(STATIC_DIR, "uploads")
    
    # 腾讯云配置
    TENCENT_SECRET_ID: str = ""
    TENCENT_SECRET_KEY: str = ""
    TENCENT_COS_REGION: str = ""
    TENCENT_COS_BUCKET: str = ""
    
    # 阿里云配置
    ALIYUN_ACCESS_KEY_ID: str = ""
    ALIYUN_ACCESS_KEY_SECRET: str = ""
    ALIYUN_SMS_SIGN_NAME: str = ""
    ALIYUN_SMS_TEMPLATE_CODE: str = ""
    
    # 日志配置
    LOG_LEVEL: str = "INFO"
    LOG_FORMAT: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    
    # API配置
    API_V1_PREFIX: str = "/api/v1"
    
    # 限流配置
    RATE_LIMIT_ENABLED: bool = True
    RATE_LIMIT_REQUESTS: int = 100
    RATE_LIMIT_PERIOD: int = 60
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        extra = "ignore"


# 创建全局设置实例
_settings = None


def get_settings() -> Settings:
    """获取设置实例（单例模式）"""
    global _settings
    if _settings is None:
        _settings = Settings()
    return _settings


# 创建模板引擎
def get_templates() -> Jinja2Templates:
    """获取模板引擎"""
    settings = get_settings()
    return Jinja2Templates(directory=settings.TEMPLATE_DIR)
