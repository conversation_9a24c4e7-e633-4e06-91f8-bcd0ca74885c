"""
安全工具模块
基于ARCHITECTURE.md规范实现的安全管理系统
"""

import jwt
from datetime import datetime, timedelta
from passlib.context import CryptContext
from typing import Optional, Dict, Any


class PasswordManager:
    """密码管理器"""

    def __init__(self):
        self.pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

    def hash_password(self, password: str) -> str:
        """加密密码"""
        return self.pwd_context.hash(password)

    def verify_password(self, plain_password: str, hashed_password: str) -> bool:
        """验证密码"""
        return self.pwd_context.verify(plain_password, hashed_password)


class JWTManager:
    """JWT管理器"""

    def __init__(self, secret_key: str, algorithm: str = "HS256", access_token_expire_minutes: int = 30):
        self.secret_key = secret_key
        self.algorithm = algorithm
        self.access_token_expire_minutes = access_token_expire_minutes

    def create_access_token(self, data: Dict[str, Any], expires_delta: Optional[timedelta] = None) -> str:
        """创建访问令牌"""
        to_encode = data.copy()
        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            expire = datetime.utcnow() + timedelta(minutes=self.access_token_expire_minutes)

        to_encode.update({"exp": expire})
        encoded_jwt = jwt.encode(to_encode, self.secret_key, algorithm=self.algorithm)
        return encoded_jwt

    def verify_token(self, token: str) -> Dict[str, Any]:
        """验证令牌"""
        try:
            payload = jwt.decode(token, self.secret_key, algorithms=[self.algorithm])
            return payload
        except jwt.ExpiredSignatureError:
            raise ValueError("Token已过期")
        except jwt.PyJWTError:
            raise ValueError("无效的Token")

    def create_refresh_token(self, data: Dict[str, Any], expires_delta: Optional[timedelta] = None) -> str:
        """创建刷新令牌"""
        to_encode = data.copy()
        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            expire = datetime.utcnow() + timedelta(days=7)  # 刷新令牌默认7天有效期

        to_encode.update({"exp": expire, "type": "refresh"})
        encoded_jwt = jwt.encode(to_encode, self.secret_key, algorithm=self.algorithm)
        return encoded_jwt


class PermissionManager:
    """权限管理器"""

    @staticmethod
    def check_permission(user, required_permission: str) -> bool:
        """检查用户权限"""
        # 超级管理员拥有所有权限
        if user.is_superuser:
            return True

        # 检查用户角色权限
        # 这里可以实现更复杂的权限检查逻辑
        return False

    @staticmethod
    def get_user_permissions(user) -> list[str]:
        """获取用户权限列表"""
        permissions = []

        if user.is_superuser:
            permissions.append("admin:*")

        # 根据用户角色获取权限
        # 这里可以实现更复杂的权限获取逻辑

        return permissions


class SecurityUtils:
    """安全工具类"""

    @staticmethod
    def generate_verification_code(length: int = 6) -> str:
        """生成验证码"""
        import random
        import string

        return "".join(random.choices(string.digits, k=length))

    @staticmethod
    def mask_sensitive_data(data: str, mask_char: str = "*", visible_chars: int = 4) -> str:
        """脱敏敏感数据"""
        if len(data) <= visible_chars:
            return mask_char * len(data)

        visible_part = data[: visible_chars // 2] + data[-(visible_chars // 2) :]
        masked_part = mask_char * (len(data) - visible_chars)
        return data[: visible_chars // 2] + masked_part + data[-(visible_chars // 2) :]

    @staticmethod
    def validate_password_strength(password: str) -> Dict[str, Any]:
        """验证密码强度"""
        import re

        result = {"is_valid": True, "score": 0, "messages": []}

        # 长度检查
        if len(password) < 8:
            result["is_valid"] = False
            result["messages"].append("密码长度至少8位")
        else:
            result["score"] += 1

        # 包含数字
        if re.search(r"\d", password):
            result["score"] += 1
        else:
            result["messages"].append("密码应包含数字")

        # 包含小写字母
        if re.search(r"[a-z]", password):
            result["score"] += 1
        else:
            result["messages"].append("密码应包含小写字母")

        # 包含大写字母
        if re.search(r"[A-Z]", password):
            result["score"] += 1
        else:
            result["messages"].append("密码应包含大写字母")

        # 包含特殊字符
        if re.search(r"[!@#$%^&*(),.?\":{}|<>]", password):
            result["score"] += 1
        else:
            result["messages"].append("密码应包含特殊字符")

        return result
